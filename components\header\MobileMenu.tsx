import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
} from '@/components/ui/sheet';
import { MenuIcon, DownloadIcon } from 'lucide-react';
import { HeaderProps } from './Header';
import { navigationItems } from '@/lib/config/navigation';
import Link from 'next/link';
import { useState } from 'react';
import { usePWAInstall } from '@/lib/hooks/usePWAInstall';

export function MobileMenu({ onImportStoryClick }: HeaderProps) {
  const [open, setOpen] = useState(false);
  const { isInstallable, install } = usePWAInstall();

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className="sm:hidden">
          <MenuIcon className="h-5 w-5" />
          <span className="sr-only">Toggle menu</span>
        </But<PERSON>>
      </SheetTrigger>
      <SheetContent side="left" className="w-[280px] sm:hidden p-0">
        <SheetHeader className="px-6 py-5.5 border-b">
          <SheetTitle className="text-lg">Menu</SheetTitle>
        </SheetHeader>
        <nav className="flex flex-col gap-1 px-4">
          {navigationItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              target={item.external ? '_blank' : undefined}
              rel={item.external ? 'noopener noreferrer' : undefined}
              onClick={() => setOpen(false)}
              className="flex items-center gap-3 px-3 py-2 text-sm rounded-md hover:bg-accent hover:text-accent-foreground transition-colors"
            >
              {item.icon}
              {item.label}
            </Link>
          ))}
          {onImportStoryClick && (
            <Button
              onClick={onImportStoryClick}
              variant="default"
              className="w-full mb-2"
            >
              Import Story
            </Button>
          )}
          {isInstallable && (
            <Button
              onClick={() => {
                install();
                setOpen(false);
              }}
              variant="outline"
              className="w-full gap-3"
            >
              <DownloadIcon className="h-4 w-4" />
              <span className="text-sm">Install App</span>
            </Button>
          )}
        </nav>
      </SheetContent>
    </Sheet>
  );
}
