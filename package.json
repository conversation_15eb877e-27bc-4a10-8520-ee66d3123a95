{"name": "osemjeden-viewer", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "next lint", "prepare": "husky", "migrate-to-redis": "pnpm tsx scripts/migrate-notion-to-redis.mts", "migrate-to-json": "pnpm tsx scripts/migrate-notion-to-json.mts"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"]}, "dependencies": {"@notionhq/client": "^2.3.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@upstash/redis": "^1.34.8", "@vercel/analytics": "^1.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "dotenv": "^16.5.0", "elevenlabs": "^1.56.0", "embla-carousel-react": "^8.5.2", "idb": "^8.0.2", "input-otp": "^1.4.2", "ioredis": "^5.6.1", "lucide-react": "^0.487.0", "next": "15.3.0", "next-themes": "^0.4.6", "posthog-js": "^1.235.0", "posthog-node": "^4.11.3", "react": "19.1.0", "react-day-picker": "^9.6.4", "react-dom": "19.1.0", "react-hook-form": "^7.55.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.2", "redis": "^4.7.0", "sonner": "^2.0.3", "tailwind-merge": "^3.1.0", "tw-animate-css": "^1.2.5", "vaul": "^1.1.2", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/ioredis": "^5.0.0", "@types/node": "^20.17.30", "@types/react": "19.1.0", "@types/react-dom": "19.1.2", "eslint": "^9", "eslint-config-next": "15.3.0", "husky": "^9.1.7", "lint-staged": "^15.5.0", "prettier": "^3.5.3", "tailwindcss": "^4", "ts-node": "^10.9.2", "tsx": "^4.19.3", "typescript": "^5.8.2"}, "pnpm": {"overrides": {"@types/react": "19.1.0", "@types/react-dom": "19.1.2"}}}