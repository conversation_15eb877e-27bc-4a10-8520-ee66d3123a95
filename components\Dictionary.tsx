'use client';

import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { SearchIcon, XIcon, BookOpenCheck, ArrowRightCircleIcon } from 'lucide-react';
import { DictionaryEntry } from '@/lib/mocks/stories/stories';

interface DictionaryProps {
  entries: DictionaryEntry[];
  title?: string;
  onWordClick?: (word: string) => void;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export const Dictionary = ({
  entries,
  title = 'Slovník',
  onWordClick,
  open = false,
  onOpenChange,
}: DictionaryProps) => {
  const [searchTerm, setSearchTerm] = useState('');

  const filteredEntries = searchTerm
    ? entries.filter(
        (entry) =>
          entry.word.toLowerCase().includes(searchTerm.toLowerCase()) ||
          entry.definition.toLowerCase().includes(searchTerm.toLowerCase()),
      )
    : entries;

  return (
    <div
      className={`fixed top-[74px] z-40 border-l border-border bg-background w-[300px] h-[calc(100vh-72px)] shadow-md flex flex-col pointer-events-auto transition-all duration-300 ease-in-out ${open ? 'right-0' : 'right-[-300px]'}`}
    >
      <div className="p-4 border-b flex items-center justify-between">
        <div className="flex items-center gap-2">
          <BookOpenCheck className="h-5 w-5" />
          <h2 className="font-semibold">{title}</h2>
        </div>
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8"
          onClick={() => onOpenChange?.(false)}
          aria-label="Close dictionary"
        >
          <ArrowRightCircleIcon className="h-5 w-5" />
        </Button>
      </div>
      <div className="p-4 overflow-hidden flex-1 flex flex-col">
        <div className="relative mb-4">
          <Input
            placeholder="Search dictionary..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pr-10"
          />
          {searchTerm ? (
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-0 top-0 h-full"
              onClick={() => setSearchTerm('')}
            >
              <XIcon className="h-4 w-4" />
            </Button>
          ) : (
            <SearchIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          )}
        </div>
        <ScrollArea className="h-[calc(100vh-220px)]">
          <div className="space-y-3 pr-2">
            {filteredEntries.length > 0 ? (
              filteredEntries.map((entry, index) => (
                <div key={index} className="border-b pb-3 last:border-b-0">
                  <div
                    className="font-medium text-primary cursor-pointer hover:underline"
                    onClick={() => onWordClick?.(entry.word)}
                  >
                    {entry.word}
                  </div>
                  <div className="text-sm mt-1">{entry.definition}</div>
                </div>
              ))
            ) : (
              <div className="text-center text-muted-foreground py-4">
                {searchTerm
                  ? 'No matching entries found'
                  : 'No dictionary entries available'}
              </div>
            )}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
};

export const DictionaryButton = ({
  onClick,
  isActive,
  className,
}: {
  onClick: () => void;
  isActive: boolean;
  className?: string;
}) => {
  return (
    <Button
      onClick={onClick}
      variant={isActive ? 'secondary' : 'outline'}
      size="sm"
      className={`flex items-center gap-1 transition-all duration-200 w-[150px] ${className || ''}`}
    >
      <BookOpenCheck size={16} />
      Dictionary: {isActive ? 'ON' : 'OFF'}
    </Button>
  );
};
