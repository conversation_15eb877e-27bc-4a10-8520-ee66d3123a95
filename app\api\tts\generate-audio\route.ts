import { NextRequest, NextResponse } from 'next/server';
import { ElevenLabsClient } from 'elevenlabs';
import {
  ELEVENLABS_PRONUNCIATION_DICTIONARY_ID,
  ELEVENLABS_PRONUNCIATION_DICTIONARY_VERSION_ID,
  VOICES,
} from '@/lib/constants';

const client = new ElevenLabsClient({
  apiKey: process.env.ELEVENLABS_API_KEY || '',
});

export async function POST(request: NextRequest) {
  const { text, voice_id, voice_settings } = await request.json();

  console.log('TEST');

  const voiceId = Object.entries(VOICES).find(
    ([_key, voiceData]) => voiceData.id === voice_id,
  )?.[1].id;

  if (!voiceId) {
    return NextResponse.json({ error: 'Voice ID is required' }, { status: 400 });
  }

  if (!text || !text.trim()) {
    return NextResponse.json({ error: 'Text is required' }, { status: 400 });
  }

  try {
    const response = await client.textToSpeech.convertWithTimestamps(voiceId, {
      text: text,
      model_id: 'eleven_multilingual_v2',
      pronunciation_dictionary_locators: [
        {
          pronunciation_dictionary_id: ELEVENLABS_PRONUNCIATION_DICTIONARY_ID,
          version_id: ELEVENLABS_PRONUNCIATION_DICTIONARY_VERSION_ID,
        },
      ],
      voice_settings: {
        ...(voice_settings.stability && { stability: voice_settings.stability }),
        ...(voice_settings.similarity_boost && {
          similarity_boost: voice_settings.similarity_boost,
        }),
        ...(voice_settings.style && { style: voice_settings.style }),
        // ...(voice_settings.style && { style: voice_settings.style }),
        // ...(voice_settings.use_speaker_boost && { use_speaker_boost: voice_settings.use_speaker_boost }),
        // ...(voice_settings.speed && { speed: voice_settings.speed }),
      },
      //language_code: "sk",
    });

    return NextResponse.json(response);
  } catch (error) {
    console.error('API Error generating TTS alignment:', error);
    return NextResponse.json(
      { error: 'API Error generating TTS alignment' },
      { status: 500 },
    );
  }
}

// https://elevenlabs.io/docs/api-reference/text-to-speech/stream-with-timestamps
// https://elevenlabs.io/docs/best-practices/prompting/controls#common-issues
