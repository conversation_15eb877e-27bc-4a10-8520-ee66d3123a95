import { useState, useEffect, useRef, useCallback } from 'react';
import { AudioTextSyncManager, Word } from '../audio/AudioTextSyncManager';

interface UseAudioTextSyncProps {
  audioUrl: string;
  words: Word[];
  metadata: {
    title: string;
    image: string;
  };
}

interface UseAudioTextSyncResult {
  isPlaying: boolean;
  isMuted: boolean;
  getCurrentWordIndex: () => number;
  currentWord: Word | null;
  currentTime: number;
  duration: number;
  volume: number;
  loop: boolean;
  playbackRate: number;
  play: () => void;
  pause: () => void;
  toggle: () => void;
  seek: (time: number) => void;
  setVolume: (level: number) => void;
  setPlaybackRate: (rate: number) => void;
  setLoop: (loop: boolean) => void;
  setMute: (mute: boolean) => void;
  isReady: boolean;
  words: Word[];
}

export function useAudioTextSync({
  audioUrl,
  words,
  metadata,
}: UseAudioTextSyncProps): UseAudioTextSyncResult {
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const audioTextSyncManagerRef = useRef<AudioTextSyncManager | null>(null);
  const animationFrameRef = useRef<number | null>(null);
  const wordsRef = useRef<Word[]>([]);

  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const currentWordIndexRef = useRef<number>(-1);
  const [currentWord, setCurrentWord] = useState<Word | null>(null);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(NaN);
  const [isAudioReady, setIsAudioReady] = useState(false);
  const [volume, setVolume] = useState(1);
  const [playbackRate, setPlaybackRate] = useState(1);
  const [loop, setLoop] = useState(false);

  const getCurrentWordIndex = useCallback(() => {
    return currentWordIndexRef.current;
  }, []);

  const findWordIndexAtTime = useCallback((time: number): number => {
    const words = wordsRef.current;
    if (!words || words.length === 0) {
      return -1;
    }

    if (words.length === 0) {
      return -1;
    }

    // 1. Check for exact match first (use <= for end time)
    // This is the most accurate case - the current time falls within a word's time range
    for (let i = 0; i < words.length; i++) {
      const word = words[i];
      if (time >= word.start && time <= word.end) {
        return word.index!; // Use the original index
      }
    }

    // 2. If no exact match: Find the closest word
    // First, check if we're before the first word
    if (time < words[0].start) {
      return -1; // Before first word, don't highlight anything
    }

    // Check if we're after the last word
    if (time > words[words.length - 1].end) {
      return words[words.length - 1].index!; // After last word, keep last word highlighted
    }

    // We're in a gap between words - find the closest word that started before current time
    let closestWord: Word | null = null;
    let closestStartTime = -Infinity;

    for (let i = 0; i < words.length; i++) {
      const word = words[i];
      if (word.start <= time && word.start > closestStartTime) {
        closestStartTime = word.start;
        closestWord = word;
      }
    }

    return closestWord ? closestWord.index! : -1;
  }, []);

  useEffect(() => {
    if (!audioUrl || !words?.length) {
      setIsPlaying(false);
      currentWordIndexRef.current = -1;
      setCurrentWord(null);
      setCurrentTime(0);
      setDuration(NaN);
      setIsAudioReady(false);
      setVolume(1);
      wordsRef.current = [];
      if (animationFrameRef.current) cancelAnimationFrame(animationFrameRef.current);
      audioTextSyncManagerRef.current?.destroy();
      audioTextSyncManagerRef.current = null;
      audioRef.current = null;
      return;
    }

    const audio = new Audio(audioUrl);
    audioRef.current = audio;
    setVolume(audio.volume);

    if ('mediaSession' in navigator && metadata.title) {
      navigator.mediaSession.metadata = new MediaMetadata({
        title: metadata.title,
        artwork: metadata.image ? [{ src: metadata.image, type: 'image/png' }] : [],
        artist: 'Osem Jeden',
      });
    }

    const audioTextSyncManager = new AudioTextSyncManager(audio, words);
    audioTextSyncManagerRef.current = audioTextSyncManager;
    wordsRef.current = audioTextSyncManager.internalWords;

    audioTextSyncManager.onPlayStateChange = (playing) => {
      setIsPlaying(playing);
    };
    audioTextSyncManager.onTimeUpdate = setCurrentTime;
    audioTextSyncManager.onLoadedData = (loadedDuration) => {
      if (!isNaN(loadedDuration)) {
        setDuration(loadedDuration);
        setIsAudioReady(true);
        if ('mediaSession' in navigator) {
          navigator.mediaSession.setPositionState({
            duration: audio.duration,
            playbackRate: audio.playbackRate,
            position: audio.currentTime,
          });
        }
      }
    };
    audioTextSyncManager.onEnded = () => {
      setIsPlaying(false);
      currentWordIndexRef.current = -1;
      setCurrentWord(null);
    };

    return () => {
      if (animationFrameRef.current) cancelAnimationFrame(animationFrameRef.current);
      audioTextSyncManager.destroy();
      audioTextSyncManagerRef.current = null;
      audioRef.current = null;
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [audioUrl]);

  useEffect(() => {
    if (!isPlaying || !audioTextSyncManagerRef.current) {
      if (animationFrameRef.current) cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
      return;
    }

    const audio = audioTextSyncManagerRef.current.rawAudioElement;

    const animationLoop = () => {
      const currentAudioTime = audio.currentTime;
      setCurrentTime(currentAudioTime);

      const newIndex = findWordIndexAtTime(currentAudioTime);

      if (newIndex !== currentWordIndexRef.current) {
        currentWordIndexRef.current = newIndex;
        setCurrentWord(newIndex === -1 ? null : wordsRef.current[newIndex]);
      }

      if (audioTextSyncManagerRef.current?.isPlaying) {
        animationFrameRef.current = requestAnimationFrame(animationLoop);
      } else {
        animationFrameRef.current = null;
      }
    };

    animationFrameRef.current = requestAnimationFrame(animationLoop);

    return () => {
      if (animationFrameRef.current) cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    };
  }, [isPlaying, findWordIndexAtTime]);

  const play = useCallback(() => audioTextSyncManagerRef.current?.play(), []);
  const pause = useCallback(() => audioTextSyncManagerRef.current?.pause(), []);
  const toggle = useCallback(() => audioTextSyncManagerRef.current?.toggle(), []);

  const seek = useCallback(
    (time: number) => {
      const audioTextSyncManager = audioTextSyncManagerRef.current;
      if (!audioTextSyncManager) return;

      audioTextSyncManager.seek(time);

      const newIndex = findWordIndexAtTime(time);

      if (newIndex !== currentWordIndexRef.current) {
        currentWordIndexRef.current = newIndex;
        setCurrentWord(newIndex === -1 ? null : wordsRef.current[newIndex]);
      }
      setCurrentTime(time);
    },
    [findWordIndexAtTime],
  );

  const handleSetVolume = useCallback((level: number) => {
    const audioTextSyncManager = audioTextSyncManagerRef.current;
    if (audioTextSyncManager) {
      const newVolume = Math.max(0, Math.min(level, 1));
      audioTextSyncManager.setVolume(newVolume);
      setVolume(newVolume);
    }
  }, []);

  const handleSetPlaybackRate = useCallback((rate: number) => {
    const audioTextSyncManager = audioTextSyncManagerRef.current;
    if (audioTextSyncManager) {
      audioTextSyncManager.setPlaybackRate(rate);
      setPlaybackRate(rate);
    }
  }, []);

  const handleSetLoop = useCallback((loopValue: boolean) => {
    const audioTextSyncManager = audioTextSyncManagerRef.current;
    if (audioTextSyncManager) {
      audioTextSyncManager.setLoop(loopValue);
      setLoop(loopValue);
    }
  }, []);

  const handleSetMute = useCallback((mute: boolean) => {
    const audioTextSyncManager = audioTextSyncManagerRef.current;
    if (audioTextSyncManager) {
      audioTextSyncManager.setMute(mute);
      setIsMuted(mute);
    }
  }, []);

  return {
    isPlaying,
    isMuted,
    getCurrentWordIndex,
    currentWord,
    currentTime,
    duration,
    volume,
    playbackRate,
    loop,
    play,
    pause,
    toggle,
    seek,
    setVolume: handleSetVolume,
    setPlaybackRate: handleSetPlaybackRate,
    setLoop: handleSetLoop,
    setMute: handleSetMute,
    isReady: isAudioReady,
    words: wordsRef.current,
  };
}
