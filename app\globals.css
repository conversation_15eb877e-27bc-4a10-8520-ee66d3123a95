@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: Nunito, sans-serif;
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --font-weight-light: 700;
  --font-weight-normal: 700;
  --font-weight-medium: 700;
  --font-weight-semibold: 700;
  --font-serif: PT Serif, serif;
  --shadow-xs: 0 2px 0 0 var(--border);
  --shadow-sm: 0 2px 0 0 var(--border);
  --shadow-md: 0 2px 0 0 var(--border);
  --shadow-lg: 0 2px 0 0 var(--border);
  --shadow-xl: 0 2px 0 0 var(--border);
  --shadow-2xl: 0 2px 0 0 var(--border);
  --shadow-3xl: 0 2px 0 0 var(--border);
  --color-destructive-border: var(--destructive-border);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-primary-border: var(--primary-border);
}

:root {
  --radius: 0.625rem;
  --background: oklch(0.91 0.048 83.6);
  --foreground: oklch(0.41 0.077 78.9);
  --card: oklch(0.92 0.042 83.6);
  --card-foreground: oklch(0.41 0.077 74.3);
  --popover: oklch(0.92 0.042 83.6);
  --popover-foreground: oklch(0.41 0.077 74.3);
  --primary: oklch(0.71 0.097 111.7);
  --primary-foreground: oklch(0.98 0.005 0);
  --secondary: oklch(0.88 0.055 83.6);
  --secondary-foreground: oklch(0.51 0.077 78.9);
  --muted: oklch(0.86 0.064 83.7);
  --muted-foreground: oklch(0.51 0.077 74.3);
  --accent: oklch(0.86 0.055 83.6);
  --accent-foreground: oklch(0.26 0.016 0);
  --destructive: oklch(0.63 0.24 29.2);
  --border: oklch(0.74 0.063 80.8);
  --input: oklch(0.74 0.063 80.8);
  --ring: oklch(0.51 0.077 74.3);
  --chart-1: oklch(0.66 0.19 41.6);
  --chart-2: oklch(0.68 0.16 184.9);
  --chart-3: oklch(0.48 0.09 210.9);
  --chart-4: oklch(0.85 0.19 85.4);
  --chart-5: oklch(0.74 0.19 66.3);
  --sidebar: oklch(0.87 0.059 83.7);
  --sidebar-foreground: oklch(0.41 0.077 78.9);
  --sidebar-primary: oklch(0.26 0.016 0);
  --sidebar-primary-foreground: oklch(0.98 0.005 0);
  --sidebar-accent: oklch(0.83 0.058 83.6);
  --sidebar-accent-foreground: oklch(0.26 0.016 0);
  --sidebar-border: oklch(0.91 0.005 0);
  --sidebar-ring: oklch(0.71 0.005 0);
  --primary-border: oklch(0.59 0.096 111.8);
  --destructive-foreground: oklch(0.97 0.018 0);
  --destructive-border: oklch(0.43 0.24 29.2);
}

.dark {
  --background: oklch(0.33 0.03 146.48);
  --foreground: oklch(0.95 0.05 85);
  --card: oklch(0.28 0.03 146.02);
  --card-foreground: oklch(0.95 0.05 85);
  --popover: oklch(0.17 0.04 145);
  --popover-foreground: oklch(0.95 0.05 85);
  --primary: oklch(0.9 0.07 85);
  --primary-foreground: oklch(0.17 0.04 145);
  --secondary: oklch(0.19 0.04 145);
  --secondary-foreground: oklch(0.95 0.05 85);
  --muted: oklch(0.19 0.04 145);
  --muted-foreground: oklch(0.85 0.05 85);
  --accent: oklch(0.9 0.07 85);
  --accent-foreground: oklch(0.17 0.04 145);
  --destructive: oklch(0.7 0.19 22.2);
  --border: oklch(0.95 0.05 85 / 85%);
  --input: oklch(0.95 0.05 85 / 85%);
  --ring: oklch(0.95 0.05 85 / 90%);
  --chart-1: oklch(0.48 0.04 145);
  --chart-2: oklch(0.69 0.04 145);
  --chart-3: oklch(0.9 0.07 85);
  --chart-4: oklch(0.62 0.04 145);
  --chart-5: oklch(0.9 0.07 85);
  --sidebar: oklch(0.17 0.04 145);
  --sidebar-foreground: oklch(0.95 0.05 85);
  --sidebar-primary: oklch(0.9 0.07 85);
  --sidebar-primary-foreground: oklch(0.17 0.04 145);
  --sidebar-accent: oklch(0.9 0.07 85);
  --sidebar-accent-foreground: oklch(0.17 0.04 145);
  --sidebar-border: oklch(0.95 0.05 85 / 85%);
  --sidebar-ring: oklch(0.95 0.05 85 / 90%);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  /* Apply transitions to the root element for all theme variables */
  :root,
  .light,
  .dark {
    transition:
      color 0.3s ease,
      background-color 0.8s ease,
      border-color 0.3s ease,
      box-shadow 0.3s ease,
      fill 0.3s ease,
      stroke 0.3s ease;
  }

  body {
    font-weight: var(--font-weight-bold);
    color: var(--foreground);
    background-color: var(--background);
  }
  .border {
    border-width: 2px !important;
  }
  .border-l {
    border-left-width: 2px !important;
  }
  .border-r {
    border-right-width: 2px !important;
  }
  .border-t {
    border-top-width: 2px !important;
  }
  .border-b {
    border-bottom-width: 2px;
  }
  .shadow-primary {
    box-shadow: 0 2px 0 0 var(--primary-border);
  }
  .shadow-destructive {
    box-shadow: 0 2px 0 0 var(--destructive);
  }
  .shadow-destructive-border {
    box-shadow: 0 2px 0 0 var(--destructive-border);
  }
  .texture {
    background-image: url(/images/texture.webp);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    opacity: 0.12;
    mix-blend-mode: multiply;
    z-index: 100;
    isolation: isolate;
    position: fixed;
    inset: 0;
    width: 100vw;
    height: 100dvh;
    pointer-events: none;

    @media (max-width: 640px) {
      background-size: cover;
    }
  }
}

button {
  @apply cursor-pointer;
}

html {
  scroll-behavior: smooth;
  height: 100%;
}

html:focus-within {
  scroll-behavior: smooth;
}

body {
  min-height: 100%;
  overflow-anchor: none;
  scrollbar-gutter: stable;
}

html body[data-scroll-locked] {
  --removed-body-scroll-bar-size: 0 !important;
  margin-right: 0 !important;
  overflow: auto !important;
}

.paragraph {
  @apply mb-4 transition-colors rounded px-2 py-1;
  border: 1px solid transparent;
}

.paragraph:hover {
  @apply bg-muted;
}

.paragraph.selected {
  @apply bg-primary/10 border-primary;
  border-width: 1px;
}

.highlighted {
  @apply text-primary;
}

@keyframes word-highlight-animation {
  0% {
    background-color: var(--primary);
    color: var(--primary-foreground);
  }
  100% {
    background-color: transparent;
    color: inherit;
  }
}

.word-flash {
  animation: word-highlight-animation 1.5s ease-out 1 forwards;
  border-radius: 2px;
}

.dark .texture {
  mix-blend-mode: hard-light;
  opacity: 0.3;
}

.dark .primary {
  box-shadow: 0 0 20px oklch(0.95 0.05 85 / 0.2);
}

.dark .highlighted {
  background-color: oklch(0.95 0.05 85 / 0.15);
  color: oklch(0.95 0.05 85);
}

.dark .highlighted::after {
  background-color: oklch(0.95 0.05 85 / 0.4);
}

.dark .paragraph:hover {
  background-color: oklch(0.99 0.01 84.74 / 0.15);
}

.dark input[type="range"] {
  background-color: oklch(0.95 0.05 85 / 40%);
  border: 2px solid oklch(0.95 0.05 85 / 85%) !important;
  box-shadow:
    0 0 0 1px oklch(0.95 0.05 85 / 40%),
    inset 0 0 0 1px oklch(0.95 0.05 85 / 20%);
}

.dark input[type="range"]::-webkit-slider-thumb {
  background-color: oklch(0.95 0.05 85);
  border: 2px solid oklch(0.95 0.05 85);
  box-shadow: 0 0 6px oklch(0.95 0.05 85 / 60%);
}

.dark input[type="range"]::-moz-range-thumb {
  background-color: oklch(0.95 0.05 85);
  border: 2px solid oklch(0.95 0.05 85);
  box-shadow: 0 0 6px oklch(0.95 0.05 85 / 60%);
}
