import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { metadata } from '@/app/layout';
import { stories } from '@/lib/mocks/stories/stories';
import StoryClient from './StoryClient';

interface PageProps {
  params: Promise<{
    id: string;
  }>;
}

export async function generateStaticParams() {
  return stories.map((story) => ({
    id: story.id,
  }));
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const storyId = (await params).id;
  const story = stories.find((story) => story.id === storyId);

  if (!story) {
    return {
      title: 'Story Not Found | Osem Jeden',
      description: 'The requested story could not be found.',
    };
  }

  return {
    ...metadata,
    title: `${story.title} | Osem Jeden`,
    description: story.description,
    alternates: {
      canonical: `https://beta.osemjeden.com/story/${story.id}`,
    },
    openGraph: {
      ...metadata.openGraph,
      images: story.imageUrl || `/opengraph-image.png`,
      title: `${story.title} | Osem <PERSON>`,
      description: story.description,
    },
  };
}

export default async function StoryPage({ params }: PageProps) {
  const storyId = (await params).id;
  const story = stories.find((story) => story.id === storyId);

  if (!story) {
    return notFound();
  }

  return <StoryClient story={story} />;
}
