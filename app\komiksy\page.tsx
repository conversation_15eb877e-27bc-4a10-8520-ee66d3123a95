import { LayoutWrapper } from '@/components/LayoutWrapper';
import { ComicStrip } from '@/components/ComicStrip';
import { comicStrips } from '@/lib/mocks/comics/comics';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Comic Strips | Osem Jeden',
  description: 'Visual comics to complement our language learning content',
  alternates: {
    canonical: 'https://beta.osemjeden.com/comics',
  },
};

export default function ComicsPage() {
  return (
    <LayoutWrapper>
      <div className="max-w-7xl mx-auto pb-0">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Comic Strips</h1>
          <p className="text-muted-foreground">
            Visual comics to complement our language learning content
          </p>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-0">
          {comicStrips.map((comic) => (
            <div key={comic.id} className="overflow-hidden">
              <ComicStrip imageUrl={comic.imageUrl} altText={comic.altText} />
            </div>
          ))}
        </div>
      </div>
    </LayoutWrapper>
  );
}
