import type { Metadata } from 'next';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { LayoutWrapper } from '@/components/LayoutWrapper';
import { RoadmapItemCard, RoadmapItem } from './RoadmapItemCard';
import roadmapItemsLocal from './roadmap-items.json';

export const metadata: Metadata = {
  title: 'Roadmap | Osem Jeden',
  description:
    "Track our progress and see what's coming next. We're constantly working to improve and add new features.",
  alternates: {
    canonical: 'https://beta.osemjeden.com/roadmap',
  },
};

export const revalidate = 86400;

export default async function RoadmapPage() {
  const roadmapItems = roadmapItemsLocal as RoadmapItem[];

  const plannedItems = roadmapItems.filter(
    (item: RoadmapItem) => item.status === 'Planned',
  );
  const inProgressItems = roadmapItems.filter(
    (item: RoadmapItem) => item.status === 'In progress',
  );
  const completedItems = roadmapItems.filter(
    (item: RoadmapItem) => item.status === 'Completed',
  );

  const roadmapSections = [
    {
      title: 'Planned',
      value: 'planned',
      items: plannedItems,
      emptyState: (
        <p className="text-sm text-muted-foreground text-center">
          Nothing planned yet...
        </p>
      ),
    },
    {
      title: 'In Progress',
      value: 'in-progress',
      items: inProgressItems,
      emptyState: (
        <p className="text-sm text-muted-foreground text-center">
          <span>Really cool stuff in progress...</span>
          <br />
          <span>You know you could just ask me, right?</span>
        </p>
      ),
    },
    {
      title: 'Completed',
      value: 'completed',
      items: completedItems,
      emptyState: (
        <p className="text-sm text-muted-foreground text-center">
          Nothing completed yet...
        </p>
      ),
    },
  ];

  return (
    <LayoutWrapper>
      <main>
        <div className="max-w-4xl mx-auto mb-12 text-center">
          <h1 className="text-4xl font-bold mb-4">Project Roadmap</h1>
          <p className="text-base text-muted-foreground">
            <span>
              Track our progress and see what's coming next. We're constantly working to
              improve and add new features.{' '}
            </span>
            <br />
            <span className="mt-1">
              <a
                href="https://github.com/Xurify"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-primary hover:bg-primary/80 transition-colors duration-200 text-primary-foreground text-sm px-2 py-1 rounded-md"
              >
                😎 Xurify
              </a>{' '}
              is also kinda cool... well sometimes 😔
            </span>
          </p>
          <div className="mt-4">
            <a
              href="https://github.com/Xurify"
              target="_blank"
              rel="noopener noreferrer"
              className="text-sm underline"
            >
              Follow development on GitHub (Closed-source atm)
            </a>
          </div>
        </div>
        <div className="hidden md:grid grid-cols-1 md:grid-cols-3 gap-6 items-start">
          {roadmapSections.map((section) => (
            <Card key={section.value}>
              <CardHeader>
                <CardTitle className="text-center">{section.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <RoadmapSectionContent
                  items={section.items}
                  emptyState={section.emptyState}
                />
              </CardContent>
            </Card>
          ))}
        </div>
        <div className="md:hidden space-y-4">
          <Accordion
            type="single"
            collapsible
            defaultValue={roadmapSections[0].value}
            className="w-full"
          >
            {roadmapSections.map((section, index) => (
              <AccordionItem
                key={section.value}
                value={section.value}
                className={`border rounded-lg overflow-hidden ${
                  index < roadmapSections.length - 1 ? 'mb-4' : ''
                }`}
              >
                <AccordionTrigger className="text-lg font-semibold px-4 py-3 hover:no-underline bg-muted/40">
                  {section.title}
                </AccordionTrigger>
                <AccordionContent className="p-4 bg-card">
                  <RoadmapSectionContent
                    items={section.items}
                    emptyState={section.emptyState}
                  />
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </main>
    </LayoutWrapper>
  );
}

const RoadmapSectionContent = ({
  items,
  emptyState,
}: {
  items: RoadmapItem[];
  emptyState: React.ReactNode;
}) => {
  if (items.length > 0) {
    return (
      <div className="space-y-3">
        {items.map((item) => (
          <RoadmapItemCard key={item.id} {...item} />
        ))}
      </div>
    );
  }
  return <>{emptyState}</>;
};
