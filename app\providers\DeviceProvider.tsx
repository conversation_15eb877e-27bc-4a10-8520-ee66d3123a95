"use client";

import { createContext, ReactNode, useContext, useEffect, useState } from "react";

interface DeviceProviderProps {
  children: ReactNode;
  initialDeviceState?: Partial<DeviceState>;
  mobileBreakpoint?: number;
}

export interface DeviceState {
  isMobile: boolean;
  isDesktop: boolean;
}

export const DeviceContext = createContext<DeviceState>({
  isMobile: false,
  isDesktop: true,

});

export function DeviceProvider({
  children,
  initialDeviceState = {},
  mobileBreakpoint = 768,
}: DeviceProviderProps) {
  const [deviceState, setDeviceState] = useState<DeviceState>({
    isMobile: false,
    isDesktop: true,
    ...initialDeviceState,
  });

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      const isMobile = width < mobileBreakpoint;

      setDeviceState({
        isMobile,
        isDesktop: !isMobile,
      });
    };

    handleResize();

    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [mobileBreakpoint]);

  return (
    <DeviceContext.Provider value={deviceState}>
      {children}
    </DeviceContext.Provider>
  );
}

export const useDevice = (): DeviceState => {
  const context = useContext(DeviceContext);
  if (context === undefined) {
    throw new Error("useDevice must be used within a DeviceProvider");
  }
  return context;
};
