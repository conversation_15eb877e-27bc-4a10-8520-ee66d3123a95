import React from 'react';
import {
  PlayIcon,
  PauseIcon,
  RotateCcwIcon,
  RotateCwIcon,
  Loader2Icon,
  Volume2Icon,
  RepeatIcon,
  SettingsIcon,
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface DesktopFloatingControlsProps {
  isPlaying: boolean;
  isReady: boolean;
  audioUrl: string;
  currentTime: number;
  duration: number;
  volume: number;
  playbackRate: number;
  loop: boolean;
  toggle: () => void;
  seek: (time: number) => void;
  setVolume: (volume: number) => void;
  setPlaybackRate: (rate: number) => void;
  setLoop: (loop: boolean) => void;
  handleRewind: () => void;
  handleForward: () => void;
  REWIND_TIME: number;
  AVAILABLE_RATES: number[];
}

export function DesktopFloatingControls({
  isPlaying,
  isReady,
  audioUrl,
  currentTime,
  duration,
  volume,
  playbackRate,
  loop,
  toggle,
  seek,
  setVolume,
  setPlaybackRate,
  setLoop,
  handleRewind,
  handleForward,
  REWIND_TIME,
  AVAILABLE_RATES,
}: DesktopFloatingControlsProps) {
  const sliderValue = isReady ? (currentTime / duration) * 100 : 0;

  const formatTime = (time: number) => {
    if (isNaN(time) || time < 0) return '0:00';
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
  };

  const handleSliderValueChange = (value: number[]) => {
    const newTime = (value[0] / 100) * duration;
    seek(newTime);
  };

  const handleVolumeChange = (value: number[]) => {
    setVolume(value[0] / 100);
  };

  const handleRateChange = (rateString: string) => {
    if (!isReady) return;
    const newRate = parseFloat(rateString);
    if (!isNaN(newRate)) {
      setPlaybackRate(newRate);
    }
  };

  return (
    <div className="fixed bottom-0 left-0 right-0 z-[10] flex justify-center">
      <div className="bg-background/90 backdrop-blur-md border rounded-t-md shadow-md max-w-2xl w-full">
        <div className="px-4 pt-3 pb-1">
          <Slider
            value={[sliderValue]}
            defaultValue={[sliderValue]}
            min={0}
            max={100}
            step={0.1}
            onValueChange={handleSliderValueChange}
            className="w-full h-1.5 cursor-pointer"
            aria-label="Audio playback position"
            disabled={!isReady}
          />
        </div>
        <div className="grid grid-cols-3 items-center px-4 py-2">
          <div className="flex items-center justify-start">
            <div className="text-sm font-mono tabular-nums">
              {formatTime(currentTime)} / {formatTime(duration)}
            </div>
          </div>

          <div className="flex justify-center items-center gap-2">
            <Button
              onClick={handleRewind}
              variant="ghost"
              size="icon"
              className="h-10 w-10 relative"
              disabled={!audioUrl || !isReady}
              aria-label={`Rewind ${REWIND_TIME} seconds`}
            >
              <RotateCcwIcon size={22} />
              <span className="absolute inset-0 flex items-center justify-center text-[10px] font-bold leading-none">
                {REWIND_TIME}
              </span>
            </Button>

            <Button
              onClick={toggle}
              variant="default"
              size="sm"
              className="h-9 w-20 flex items-center justify-center gap-1"
              disabled={!audioUrl || !isReady}
            >
              {!isReady && audioUrl ? (
                <Loader2Icon className="h-4 w-4 animate-spin" />
              ) : (
                <>
                  {isPlaying ? <PauseIcon size={16} /> : <PlayIcon size={16} />}
                  <span>{isPlaying ? 'Pause' : 'Play'}</span>
                </>
              )}
            </Button>

            <Button
              onClick={handleForward}
              variant="ghost"
              size="icon"
              className="h-10 w-10 relative"
              disabled={!audioUrl || !isReady}
              aria-label={`Forward ${REWIND_TIME} seconds`}
            >
              <RotateCwIcon size={22} />
              <span className="absolute inset-0 flex items-center justify-center text-[10px] font-bold leading-none">
                {REWIND_TIME}
              </span>
            </Button>
          </div>

          <div className="flex items-center justify-end gap-2">
            <Button
              onClick={() => setLoop(!loop)}
              variant={loop ? 'secondary' : 'ghost'}
              size="icon"
              className="h-8 w-8"
              title={`Loop: ${loop ? 'ON' : 'OFF'}`}
            >
              <RepeatIcon size={16} className={loop ? 'text-primary' : ''} />
            </Button>

            <div className="flex items-center gap-2 w-24">
              <Volume2Icon size={14} className="text-muted-foreground flex-shrink-0" />
              <div className="flex-1">
                <Slider
                  value={[volume * 100]}
                  onValueChange={handleVolumeChange}
                  min={0}
                  max={100}
                  step={1}
                  className="w-full"
                  aria-label="Audio volume"
                  disabled={!audioUrl}
                />
              </div>
            </div>

            <Popover>
              <PopoverTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8" title="Settings">
                  <SettingsIcon size={16} />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-60 z-[11]" align="end">
                <div className="space-y-4 py-2">
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Playback Speed</h4>
                    <Select
                      value={playbackRate.toString()}
                      onValueChange={handleRateChange}
                      disabled={!audioUrl || !isReady}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Speed" />
                      </SelectTrigger>
                      <SelectContent className="z-[11]">
                        {AVAILABLE_RATES.map((rate) => (
                          <SelectItem key={rate} value={rate.toString()}>
                            {rate}x
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>
        </div>
      </div>
    </div>
  );
}
