import Image from 'next/image';
import Link from 'next/link';
import { BookOpenIcon } from 'lucide-react';
import { Button } from './ui/button';
import { Card } from './ui/card';
import { CardContent, CardTitle } from './ui/card';
import { truncate } from '@/lib/utils/strings';
import { Story } from '@/lib/mocks/stories/stories';

interface StoryCardProps {
  story: Story;
  index: number;
}

export function StoryCard({ story, index }: StoryCardProps) {
  return (
    <Link href={`/story/${story.id}`}>
      <Card className="pt-0 pb-2 gap-2 overflow-hidden transition-all duration-200 hover:shadow-md group h-full flex flex-col">
        <div className="relative w-full pt-[70%] overflow-hidden">
          <Image
            src={story.imageUrl}
            alt={story.title}
            fill
            loading={index < 3 ? 'eager' : 'lazy'}
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            className="object-cover transition-transform duration-300 group-hover:scale-105"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end p-3">
            <div className="flex">
              <Button size="sm" variant="default">
                <span>
                  <BookOpenIcon className="h-4 w-4 mr-1 inline-block align-middle" />
                  <span className="align-middle">Read</span>
                </span>
              </Button>
            </div>
          </div>
        </div>
        <CardContent className="p-3 pt-2 flex-grow flex flex-col justify-between">
          <CardTitle className="text-base font-medium">{story.title}</CardTitle>
          <p className="text-muted-foreground text-xs mt-1 line-clamp-2">
            {truncate(story.description, 95)}
          </p>
        </CardContent>
      </Card>
    </Link>
  );
}
