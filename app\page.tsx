import { stories } from '@/lib/mocks/stories/stories';
import { LayoutWrapper } from '@/components/LayoutWrapper';
import { StoryCard } from '@/components/StoryCard';

export default function Home() {
  return (
    <LayoutWrapper>
      <div className="max-w-7xl mx-auto px-4 pb-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Story Library</h1>
          <p className="text-muted-foreground">Choose a story to read and listen to</p>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {stories.map((story, index) => (
            <StoryCard key={story.id} story={story} index={index} />
          ))}
        </div>
      </div>
    </LayoutWrapper>
  );
}
