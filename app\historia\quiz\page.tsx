'use client';

import { useState } from 'react';
import { LayoutWrapper } from '@/components/LayoutWrapper';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  BrainIcon,
  CheckCircleIcon,
  XCircleIcon,
  TrophyIcon,
  RefreshCwIcon
} from 'lucide-react';

interface QuizQuestion {
  id: string;
  question: string;
  englishQuestion: string;
  options: string[];
  correctAnswer: number;
  explanation: string;
  englishExplanation: string;
  difficulty: 'easy' | 'medium' | 'hard';
  category: string;
}

const quizQuestions: QuizQuestion[] = [
  {
    id: 'slavs-arrival',
    question: 'V ktorom storočí prišli Slovania na územie dnešného Slovenska?',
    englishQuestion: 'In which century did the Slavs arrive in the territory of present-day Slovakia?',
    options: ['3.-4. storo<PERSON><PERSON>', '5.-6. stor<PERSON><PERSON><PERSON>', '7.-8. storo<PERSON><PERSON>', '9.-10. storo<PERSON><PERSON>'],
    correctAnswer: 1,
    explanation: 'Slovania prišli na naše územie v 5.-6. storočí po Kristovi z východoeurópskych stepí.',
    englishExplanation: 'The Slavs came to our territory in the 5th-6th century AD from Eastern European steppes.',
    difficulty: 'easy',
    category: 'Príchod Slovanov'
  },
  {
    id: 'samo-empire-duration',
    question: 'Ako dlho trvala Samova ríša?',
    englishQuestion: 'How long did Samo\'s Empire last?',
    options: ['25 rokov', '35 rokov', '45 rokov', '55 rokov'],
    correctAnswer: 1,
    explanation: 'Samova ríša trvala 35 rokov, od roku 623 do 658 po Kristovi.',
    englishExplanation: 'Samo\'s Empire lasted 35 years, from 623 to 658 AD.',
    difficulty: 'medium',
    category: 'Samova ríša'
  },
  {
    id: 'cyril-methodius-script',
    question: 'Aké písmo vytvoril Konštantín (Cyril)?',
    englishQuestion: 'What script did Constantine (Cyril) create?',
    options: ['Latinku', 'Glagoliku', 'Cyriliku', 'Runy'],
    correctAnswer: 1,
    explanation: 'Konštantín vytvoril glagolské písmo, ktoré bolo prispôsobené slovanskému jazyku.',
    englishExplanation: 'Constantine created the Glagolitic script, which was adapted to the Slavic language.',
    difficulty: 'medium',
    category: 'Veľká Morava'
  },
  {
    id: 'great-moravia-peak',
    question: 'Kto vládol Veľkej Morave v čase jej najväčšieho rozmachu?',
    englishQuestion: 'Who ruled Great Moravia during its greatest expansion?',
    options: ['Mojmír I.', 'Rastislav', 'Svätopluk I.', 'Mojmír II.'],
    correctAnswer: 2,
    explanation: 'Svätopluk I. vládol v rokoch 870-894 a za jeho vlády dosiahla Veľká Morava najväčší územný rozsah.',
    englishExplanation: 'Svatopluk I ruled from 870-894 and during his reign Great Moravia reached its greatest territorial extent.',
    difficulty: 'hard',
    category: 'Veľká Morava'
  },
  {
    id: 'great-moravia-end',
    question: 'Čo spôsobilo zánik Veľkej Moravy?',
    englishQuestion: 'What caused the fall of Great Moravia?',
    options: ['Občianska vojna', 'Maďarské nájazdy', 'Prírodné katastrofy', 'Ekonomická kríza'],
    correctAnswer: 1,
    explanation: 'Veľká Morava zanikla v roku 907 kvôli nájazdov maďarských kmeňov.',
    englishExplanation: 'Great Moravia fell in 907 due to raids by Hungarian tribes.',
    difficulty: 'easy',
    category: 'Veľká Morava'
  }
];

const difficultyColors = {
  easy: 'bg-green-500',
  medium: 'bg-yellow-500',
  hard: 'bg-red-500'
};

const difficultyLabels = {
  easy: 'Ľahké',
  medium: 'Stredné',
  hard: 'Ťažké'
};

export default function QuizPage() {
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  const [showResult, setShowResult] = useState(false);
  const [score, setScore] = useState(0);
  const [answers, setAnswers] = useState<boolean[]>([]);
  const [quizCompleted, setQuizCompleted] = useState(false);

  const question = quizQuestions[currentQuestion];
  const progress = ((currentQuestion + 1) / quizQuestions.length) * 100;

  const handleAnswerSelect = (answerIndex: number) => {
    if (showResult) return;
    setSelectedAnswer(answerIndex);
  };

  const handleSubmitAnswer = () => {
    if (selectedAnswer === null) return;
    
    const isCorrect = selectedAnswer === question.correctAnswer;
    const newAnswers = [...answers, isCorrect];
    setAnswers(newAnswers);
    
    if (isCorrect) {
      setScore(score + 1);
    }
    
    setShowResult(true);
  };

  const handleNextQuestion = () => {
    if (currentQuestion < quizQuestions.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
      setSelectedAnswer(null);
      setShowResult(false);
    } else {
      setQuizCompleted(true);
    }
  };

  const resetQuiz = () => {
    setCurrentQuestion(0);
    setSelectedAnswer(null);
    setShowResult(false);
    setScore(0);
    setAnswers([]);
    setQuizCompleted(false);
  };

  const getScoreMessage = () => {
    const percentage = (score / quizQuestions.length) * 100;
    if (percentage >= 80) return { message: 'Výborné!', icon: '🏆', color: 'text-yellow-500' };
    if (percentage >= 60) return { message: 'Dobré!', icon: '⭐', color: 'text-blue-500' };
    if (percentage >= 40) return { message: 'Slušné!', icon: '👍', color: 'text-green-500' };
    return { message: 'Skúste znovu!', icon: '💪', color: 'text-gray-500' };
  };

  if (quizCompleted) {
    const scoreMessage = getScoreMessage();
    
    return (
      <LayoutWrapper>
        <div className="max-w-2xl mx-auto pb-8">
          <Card>
            <CardHeader className="text-center">
              <div className="mb-4">
                <TrophyIcon className={`h-16 w-16 mx-auto ${scoreMessage.color}`} />
              </div>
              <CardTitle className="text-2xl">Kvíz dokončený!</CardTitle>
              <p className="text-muted-foreground">Pozrite si svoje výsledky</p>
            </CardHeader>
            <CardContent className="text-center space-y-6">
              <div>
                <div className="text-4xl font-bold mb-2">
                  {score} / {quizQuestions.length}
                </div>
                <div className={`text-xl ${scoreMessage.color}`}>
                  {scoreMessage.icon} {scoreMessage.message}
                </div>
                <div className="text-sm text-muted-foreground mt-2">
                  {Math.round((score / quizQuestions.length) * 100)}% správnych odpovedí
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="font-medium">Prehľad odpovedí:</h3>
                <div className="grid grid-cols-5 gap-2 justify-center">
                  {answers.map((isCorrect, index) => (
                    <div
                      key={index}
                      className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm ${
                        isCorrect ? 'bg-green-500' : 'bg-red-500'
                      }`}
                    >
                      {index + 1}
                    </div>
                  ))}
                </div>
              </div>

              <Button onClick={resetQuiz} className="w-full">
                <RefreshCwIcon className="h-4 w-4 mr-2" />
                Skúsiť znovu
              </Button>
            </CardContent>
          </Card>
        </div>
      </LayoutWrapper>
    );
  }

  return (
    <LayoutWrapper>
      <div className="max-w-2xl mx-auto pb-8">
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-2xl font-bold">Historický kvíz</h1>
            <Badge className={`${difficultyColors[question.difficulty]} text-white`}>
              {difficultyLabels[question.difficulty]}
            </Badge>
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>Otázka {currentQuestion + 1} z {quizQuestions.length}</span>
              <span>Skóre: {score}/{currentQuestion + (showResult ? 1 : 0)}</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        </div>

        <Card>
          <CardHeader>
            <div className="flex items-center gap-2 mb-2">
              <BrainIcon className="h-5 w-5" />
              <Badge variant="outline">{question.category}</Badge>
            </div>
            <CardTitle className="text-lg">{question.question}</CardTitle>
            <p className="text-sm text-muted-foreground italic">
              {question.englishQuestion}
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              {question.options.map((option, index) => (
                <Button
                  key={index}
                  variant={selectedAnswer === index ? "default" : "outline"}
                  className={`w-full justify-start text-left h-auto p-4 ${
                    showResult
                      ? index === question.correctAnswer
                        ? 'bg-green-500 text-white border-green-500'
                        : selectedAnswer === index && index !== question.correctAnswer
                        ? 'bg-red-500 text-white border-red-500'
                        : ''
                      : ''
                  }`}
                  onClick={() => handleAnswerSelect(index)}
                  disabled={showResult}
                >
                  <div className="flex items-center gap-3">
                    <div className="w-6 h-6 rounded-full border-2 flex items-center justify-center text-xs font-bold">
                      {String.fromCharCode(65 + index)}
                    </div>
                    <span>{option}</span>
                    {showResult && index === question.correctAnswer && (
                      <CheckCircleIcon className="h-5 w-5 ml-auto" />
                    )}
                    {showResult && selectedAnswer === index && index !== question.correctAnswer && (
                      <XCircleIcon className="h-5 w-5 ml-auto" />
                    )}
                  </div>
                </Button>
              ))}
            </div>

            {showResult && (
              <div className="mt-4 p-4 bg-muted rounded-lg">
                <h4 className="font-medium mb-2">Vysvetlenie:</h4>
                <p className="text-sm mb-2">{question.explanation}</p>
                <p className="text-xs text-muted-foreground italic">
                  {question.englishExplanation}
                </p>
              </div>
            )}

            <div className="flex gap-2">
              {!showResult ? (
                <Button 
                  onClick={handleSubmitAnswer} 
                  disabled={selectedAnswer === null}
                  className="flex-1"
                >
                  Potvrdiť odpoveď
                </Button>
              ) : (
                <Button onClick={handleNextQuestion} className="flex-1">
                  {currentQuestion < quizQuestions.length - 1 ? 'Ďalšia otázka' : 'Dokončiť kvíz'}
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </LayoutWrapper>
  );
}
