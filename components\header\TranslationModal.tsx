import React, { useState } from 'react';
import { toast } from 'sonner';
import { LanguagesIcon, UploadIcon } from 'lucide-react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { stories } from '@/lib/mocks/stories/stories';

interface TranslationModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const TranslationModal: React.FC<TranslationModalProps> = ({ isOpen, onClose }) => {
  const [sourceText, setSourceText] = useState('');
  const [translatedText, setTranslatedText] = useState('');
  const [isTranslating, setIsTranslating] = useState(false);

  const handleTranslateText = () => {
    if (sourceText.trim() === '') return;

    toast.warning('Sorry, this feature is not available yet.', {
      duration: 4000,
    });
    return;

    setIsTranslating(true);

    setTimeout(() => {
      if (translatedText.trim() === '') {
        if (sourceText === stories[0].text) {
          setTranslatedText(stories[0].translation);
        } else {
          setTranslatedText(
            'This is a simulated translation. In a real application, this text would be translated using a service like Google Translate API.',
          );
        }
      }

      setIsTranslating(false);

      toast.success('Translation complete', {
        description: 'Your text has been translated successfully.',
        duration: 3000,
      });
    }, 1000);
  };

  const handleSourceTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setSourceText(e.target.value);
  };

  const handleTranslatedTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setTranslatedText(e.target.value);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-full sm:max-w-6xl">
        <DialogHeader>
          <DialogTitle>Edit Text</DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <div className="flex justify-between items-center h-7">
              <Label>Source Text</Label>
              <Button
                className="flex items-center gap-2 h-7"
                onClick={handleTranslateText}
                variant="default"
                size="sm"
                disabled={isTranslating}
              >
                <LanguagesIcon size={14} />
                {isTranslating ? 'Translating...' : 'Translate'}
              </Button>
            </div>
            <ScrollArea className="h-[300px] md:h-[500px] border rounded-md">
              <Textarea
                value={sourceText}
                onChange={handleSourceTextChange}
                className="h-full min-h-[300px] md:min-h-[500px] resize-none border-0 focus-visible:ring-0 p-4"
                disabled={true}
                placeholder="Source text"
              />
            </ScrollArea>
          </div>
          <div className="space-y-2 hidden md:block">
            <div className="flex items-center h-7">
              <Label>Translated Text</Label>
            </div>
            <ScrollArea className="h-[300px] md:h-[500px] border rounded-md">
              <Textarea
                value={translatedText}
                onChange={handleTranslatedTextChange}
                className="h-full min-h-[300px] md:min-h-[500px] resize-none border-0 focus-visible:ring-0 p-4"
                disabled={true}
                placeholder="Translated text"
              />
            </ScrollArea>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row justify-end gap-4 mt-6">
          <Button
            className="flex items-center gap-2 w-full sm:w-auto"
            variant="default"
            onClick={() => {
              // TODO: Implement file import functionality
              toast.info('Text import feature coming soon!');
            }}
          >
            <UploadIcon size={16} />
            Import
          </Button>
          <Button onClick={onClose} variant="outline" className="w-full sm:w-auto">
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TranslationModal;
