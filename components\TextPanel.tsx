'use client';

import React, { memo, RefObject, useRef, useMemo, useState } from 'react';
import { Loader2Icon, CopyIcon, CheckIcon } from 'lucide-react';
import type { Word } from '@/lib/audio/AudioTextSyncManager';
import { DictionaryEntry } from '@/lib/mocks/stories/stories';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils/strings';
import {
  ENGLISH_TEXT_MARKER,
  splitWordAndPunctuation,
  stripEnglishTextMarkers,
} from '@/lib/utils/text-panel';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface TextPanelProps {
  title: string;
  content: string;
  words: Word[];
  currentWordIndex: number;
  fontSize?: number;
  isTranslating?: boolean;
  highlightingEnabled?: boolean;
  panelRef?: RefObject<HTMLDivElement | null>;
  textContentRef?: RefObject<HTMLDivElement | null>;
  onWordClick?: (word: Word) => void;
  dictionary?: DictionaryEntry[];
}

export const TextPanel = memo(function TextPanel({
  title,
  content,
  words,
  currentWordIndex,
  fontSize = 16,
  isTranslating = false,
  highlightingEnabled = true,
  panelRef,
  textContentRef,
  onWordClick,
  dictionary = [],
}: TextPanelProps) {
  const scrollViewportRef = useRef<HTMLDivElement | null>(null);
  const currentWordRef = useRef<HTMLSpanElement | null>(null);
  const [isCopied, setIsCopied] = useState(false);

  const renderedContent = useMemo(() => {
    if (!content) return null;

    const finalParagraphs: React.ReactNode[] = [];
    let wordSearchIndex = 0;

    const paragraphs = content.split('\n\n');

    paragraphs.forEach((paragraph, paragraphIndex) => {
      const currentParagraphNodes: React.ReactNode[] = [];
      let lastParagraphIndex = 0; // Index within the current paragraph's text

      const isEnglishParagraph = paragraph.includes(ENGLISH_TEXT_MARKER);
      const paragraphText = stripEnglishTextMarkers(paragraph);
      if (!paragraphText.trim()) return;

      // Determine if the first paragraph's rendering should be skipped
      const shouldSkipRenderingThisParagraph =
        paragraphIndex === 0 && paragraphText.trim() === title.trim();

      // --- Process words belonging to this paragraph (always process to advance index) ---
      while (wordSearchIndex < words.length) {
        const currentWord = words[wordSearchIndex];

        // Skip words that are only whitespace/newlines
        if (currentWord.text.trim() === '') {
          wordSearchIndex++;
          continue;
        }

        // Normalize word text for searching (e.g., handle escaped quotes)
        const wordTextToFind = currentWord.text.replace(/\\"/g, '"');
        let wordStartIndex = -1;

        // Search for the word starting from the last index within this paragraph
        try {
          wordStartIndex = paragraphText.indexOf(wordTextToFind, lastParagraphIndex);
        } catch (e) {
          console.error(
            'Error finding word:',
            { paragraphText, wordTextToFind, lastParagraphIndex },
            e,
          );
          // Attempt to recover or skip? For now, just break loop for this para
          break;
        }

        // If word not found in the remainder of this paragraph, stop processing words for this paragraph
        if (wordStartIndex === -1) {
          break;
        }

        // 1. Add text segment before the found word
        if (wordStartIndex > lastParagraphIndex) {
          const textSegment = paragraphText.substring(lastParagraphIndex, wordStartIndex);
          // Add text span, applying English style if needed
          if (!shouldSkipRenderingThisParagraph) {
            currentParagraphNodes.push(
              <span
                key={`p${paragraphIndex}-text-${lastParagraphIndex}`}
                className={cn(isEnglishParagraph && 'text-muted-foreground')}
              >
                {!textSegment.trim() ? ' ' : ''}
              </span>,
            );
          }
        }

        // 2. Process and add the word (including punctuation)
        //const { start, end } = currentWord;
        //const titleText = `${start.toFixed(2)}s - ${end.toFixed(2)}s`;

        // Use the *original* word text for splitting punctuation
        const { leadingPunct, coreWord, trailingPunct } = splitWordAndPunctuation(
          currentWord.text,
        );

        const isCurrentWord = wordSearchIndex === currentWordIndex;

        const isDictionaryWord = dictionary.some(
          (entry) => entry.word.toLowerCase() === coreWord.toLowerCase(),
        );

        if (!shouldSkipRenderingThisParagraph) {
          if (leadingPunct) {
            currentParagraphNodes.push(
              <span
                key={`p${paragraphIndex}-word-${wordSearchIndex}-leading`}
                //title={titleText}
                className={cn(isEnglishParagraph && 'text-muted-foreground')}
              >
                {leadingPunct}
              </span>,
            );
          }

          if (coreWord) {
            currentParagraphNodes.push(
              <span
                key={`p${paragraphIndex}-word-${wordSearchIndex}-core`}
                ref={isCurrentWord ? currentWordRef : null}
                data-word-index={wordSearchIndex}
                className={cn(
                  'md:cursor-pointer box-border',
                  isCurrentWord &&
                    highlightingEnabled &&
                    'bg-primary text-primary-foreground rounded',
                  isDictionaryWord &&
                    'underline decoration-dotted decoration-primary underline-offset-4',
                  isEnglishParagraph && 'text-muted-foreground',
                )}
                //title={titleText}
                onClick={() => {
                  onWordClick?.(currentWord);
                }}
              >
                {coreWord}
              </span>,
            );
          }

          if (trailingPunct) {
            currentParagraphNodes.push(
              <span
                key={`p${paragraphIndex}-word-${wordSearchIndex}-trailing`}
                //title={titleText}
                className={cn(isEnglishParagraph && 'text-muted-foreground')}
              >
                {trailingPunct}
              </span>,
            );
          }
        }

        // 3. Update indices for the next search within the paragraph
        lastParagraphIndex = wordStartIndex + wordTextToFind.length;
        wordSearchIndex++; // IMPORTANT: Move to the next word in the global `words` array
      }

      // --- Add any remaining text in the paragraph after the last word ---
      if (lastParagraphIndex < paragraphText.length) {
        const remainingText = paragraphText.substring(lastParagraphIndex);
        if (!shouldSkipRenderingThisParagraph) {
          currentParagraphNodes.push(
            <span
              key={`p${paragraphIndex}-text-remaining`}
              className={cn(isEnglishParagraph && 'text-muted-foreground')}
            >
              {remainingText}
            </span>,
          );
        }
      }

      // --- Finalize the paragraph ---
      // ---> MODIFIED: Conditionally add paragraph <---
      if (!shouldSkipRenderingThisParagraph && currentParagraphNodes.length > 0) {
        finalParagraphs.push(
          <p key={`para-${paragraphIndex}`} className="mb-4 whitespace-pre-wrap">
            {currentParagraphNodes}
          </p>,
        );
      }
    });

    return finalParagraphs;
  }, [
    content,
    words,
    currentWordIndex,
    highlightingEnabled,
    dictionary,
    onWordClick,
    title,
  ]);

  const handleCopy = () => {
    const formattedContent = stripEnglishTextMarkers(content).replaceAll('\n\n\n', '\n');
    navigator.clipboard
      .writeText(formattedContent)
      .then(() => {
        setIsCopied(true);
        setTimeout(() => setIsCopied(false), 2000);
      })
      .catch((error) => {
        console.error('Failed to copy text: ', error);
      });
  };

  return (
    <Card className="flex-1 border-0 border-b-0 shadow-none md:shadow-sm md:border-2 bg-transparent md:bg-card py-0 md:py-6 gap-4 md:gap-6">
      {title && (
        <CardHeader className="flex items-center justify-between">
          <CardTitle className="text-lg">{title}</CardTitle>
          <div className="flex items-center gap-1 ml-auto">
            <TooltipProvider>
              <Tooltip delayDuration={100}>
                <TooltipTrigger asChild>
                  <Button
                    onClick={handleCopy}
                    variant="ghost"
                    size="icon"
                    className="h-9 w-9 md:h-8 md:w-8"
                  >
                    {isCopied ? (
                      <CheckIcon className="text-green-500 w-5 h-5 md:w-4 md:h-4" />
                    ) : (
                      <CopyIcon className="w-5 h-5 md:w-4 md:h-4" />
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{isCopied ? 'Copied!' : 'Copy text'}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </CardHeader>
      )}
      <CardContent className="flex-1">
        <ScrollArea ref={panelRef} className="h-[calc(100vh-212px)]">
          <div ref={scrollViewportRef}>
            {isTranslating ? (
              <div className="flex items-center justify-center h-full">
                <Loader2Icon className="mr-2 h-8 w-8 animate-spin" />
                <span>Translating...</span>
              </div>
            ) : (
              <div
                ref={textContentRef}
                className="text-content-wrapper"
                style={{ fontSize: `${fontSize}px` }}
              >
                {renderedContent}
              </div>
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
});
