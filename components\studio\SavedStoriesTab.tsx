'use client';

import { useState } from 'react';
import { Loader2Icon, TrashIcon, PlusIcon, BookOpenIcon, PencilIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useIndexedDBStories } from '@/lib/hooks/useIndexedDBStories';
import { ExtendedStory } from '@/lib/db';

interface SavedStoriesTabProps {
  onNewStory: () => void;
  onLoadStory: (story: ExtendedStory) => void;
}

export default function SavedStoriesTab({
  onNewStory,
  onLoadStory,
}: SavedStoriesTabProps) {
  const { stories, isLoading, deleteStory } = useIndexedDBStories();
  const [storyToDelete, setStoryToDelete] = useState<{
    id: string;
    title: string;
  } | null>(null);

  const handleDeleteStory = async () => {
    if (!storyToDelete) return;
    await deleteStory(storyToDelete.id, storyToDelete.title);
    setStoryToDelete(null);
  };

  const formatDate = (date: Date | undefined) => {
    if (!date) return 'Unknown';
    return new Date(date).toLocaleString();
  };

  return (
    <>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-semibold">Your Stories</h2>
        <Button onClick={onNewStory} className="gap-2">
          <PlusIcon className="h-4 w-4" />
          New Story
        </Button>
      </div>

      <Card>
        <CardContent className="pt-6">
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2Icon className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : stories.length === 0 ? (
            <div className="text-center py-16 px-4">
              <div className="mb-4 text-muted-foreground">
                <BookOpenIcon className="h-12 w-12 mx-auto mb-2" />
              </div>
              <h3 className="text-xl font-medium mb-2">No stories yet</h3>
              <p className="text-muted-foreground mb-6">
                Create your first story to get started
              </p>
              <Button onClick={onNewStory} className="gap-2">
                <PlusIcon className="h-4 w-4" />
                Create New Story
              </Button>
            </div>
          ) : (
            <div className="overflow-hidden">
              <ScrollArea className="h-[calc(100vh-300px)] min-h-[400px]">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[40%] sticky top-0 bg-background">
                        Title
                      </TableHead>
                      <TableHead className="w-[30%] sticky top-0 bg-background">
                        Description
                      </TableHead>
                      <TableHead className="w-[15%] sticky top-0 bg-background">
                        Last Modified
                      </TableHead>
                      <TableHead className="w-[15%] text-right sticky top-0 bg-background">
                        Actions
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {stories.map((story) => (
                      <TableRow
                        key={story.id}
                        className="cursor-pointer hover:bg-muted/50"
                        onClick={() => onLoadStory(story)}
                      >
                        <TableCell className="font-medium">{story.title}</TableCell>
                        <TableCell className="text-muted-foreground line-clamp-1">
                          {story.description || 'No description'}
                        </TableCell>
                        <TableCell className="text-xs text-muted-foreground">
                          {formatDate(story.dateModified as unknown as Date)}
                        </TableCell>
                        <TableCell className="text-right">
                          <div
                            className="flex justify-end space-x-2"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              title="Edit story"
                              onClick={() => onLoadStory(story)}
                            >
                              <PencilIcon className="h-4 w-4" />
                            </Button>
                            <AlertDialog
                              open={storyToDelete?.id === story.id}
                              onOpenChange={(open: boolean) => {
                                if (!open) setStoryToDelete(null);
                              }}
                            >
                              <AlertDialogTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8 text-destructive hover:text-destructive"
                                  title="Delete story"
                                  onClick={() =>
                                    setStoryToDelete({ id: story.id, title: story.title })
                                  }
                                >
                                  <TrashIcon className="h-4 w-4" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Delete Story</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Are you sure you want to delete "{story.title}"? This
                                    action cannot be undone.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction onClick={handleDeleteStory}>
                                    Delete
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </ScrollArea>
            </div>
          )}
        </CardContent>
      </Card>
    </>
  );
}
