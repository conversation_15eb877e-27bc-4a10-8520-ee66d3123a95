import { useRef, useEffect } from 'react';

interface UseSoundEffectProps {
  audioUrl: string;
  volume?: number;
}

export function useSoundEffect({ audioUrl, volume = 0.5 }: UseSoundEffectProps) {
  const audioRef = useRef<HTMLAudioElement | null>(null);

  useEffect(() => {
    if (!audioRef.current) {
      audioRef.current = new Audio(audioUrl);
      audioRef.current.volume = volume;
      audioRef.current.preload = 'auto';
    }

    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
    };
  }, [audioUrl, volume]);

  const play = () => {
    if (!audioRef.current) return;
    
    audioRef.current.currentTime = 0;
    audioRef.current.play().catch(error => {
      console.error('Error playing sound effect:', error);
    });
  };

  return play;
} 