import { MetadataRoute } from 'next';

export default function manifest(): MetadataRoute.Manifest {
  return {
    name: '<PERSON>se<PERSON>',
    short_name: 'Ose<PERSON>',
    description:
      'Learn Slovak through short engaging stories. Improve your comprehension, listening skills, and enhance your language learning experience.',
    start_url: '/',
    display: 'standalone',
    display_override: ['standalone'],
    background_color: '#E0CDAB',
    //theme_color: '#E0CDAB',
    icons: [
      {
        src: '/icons/osemjeden.png',
        sizes: '1024x1024',
        type: 'image/png',
      },
      {
        src: '/icons/maskable_icon_x512.png',
        sizes: '512x512',
        type: 'image/png',
        purpose: 'maskable',
      },
    ],
    categories: ['education', 'language', 'learning', 'slovak'],
    screenshots: [
      {
        src: '/images/screenshots/osemjeden_desktop_screenshot_light.png',
        sizes: '1920x1080',
        type: 'image/png',
        label: 'Home page of Osem Jeden',
        form_factor: 'wide',
      },
      {
        src: '/images/screenshots/osemjeden_mobile_screenshot_light.png',
        sizes: '430x932',
        type: 'image/png',
        label: 'Home page of Osem Jeden',
        form_factor: 'narrow',
      },
      {
        src: '/images/screenshots/osemjeden_desktop_storypage_screenshot_light.png',
        sizes: '1920x1080',
        type: 'image/png',
        label: 'Home page of Osem Jeden',
        form_factor: 'wide',
      },
      {
        src: '/images/screenshots/osemjeden_mobile_storypage_screenshot_light.png',
        sizes: '430x932',
        type: 'image/png',
        label: 'Home page of Osem Jeden',
        form_factor: 'narrow',
      },
    ],
    prefer_related_applications: false,
  };
}
