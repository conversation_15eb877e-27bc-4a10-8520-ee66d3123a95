import { cn } from "./strings";

export const ENGLISH_TEXT_MARKER = "__ENGLISH_TEXT_START__";
export const ENGLISH_TEXT_END_MARKER = "__ENGLISH_TEXT_END__";

/**
 * Splits a text string into leading punctuation, core word, and trailing punctuation.
 * Handles potential escaped quotes within the word text itself.
 *
 * @param text - The input text string to split.
 * @returns An object containing the leading punctuation, core word, and trailing punctuation.
 */
export function splitWordAndPunctuation(text: string): {
  leadingPunct: string;
  coreWord: string;
  trailingPunct: string;
} {
  // Handles potential escaped quotes within the word text itself
  const normalizedText = text.replace(/\\"/g, '"');
  // Regex to capture leading punctuation (group 1), core word (group 2), and trailing punctuation (group 3)
  const match = normalizedText.match(
    /^([.,!?"':;()\[\]{}/\\]+|\p{P}*)(.*?)([.,!?"':;()\[\]{}/\\]+|\p{P}*)$/u,
  );

  let leadingPunct = "";
  let coreWord = normalizedText; // Default to full normalized text if no match
  let trailingPunct = "";

  if (match) {
    leadingPunct = match[1] || "";
    coreWord = match[2] || ""; // Group 2 is the core word
    trailingPunct = match[3] || "";
  }

  // Handle cases where the 'word' might be only punctuation or empty after matching
  if (!coreWord && (leadingPunct || trailingPunct)) {
    coreWord = leadingPunct || trailingPunct; // Assign all punctuation to coreWord
    leadingPunct = "";
    trailingPunct = "";
  } else if (
    !coreWord &&
    normalizedText.length > 0 &&
    !leadingPunct &&
    !trailingPunct
  ) {
    // If coreWord is empty but original text wasn't just punctuation
    coreWord = normalizedText;
  }

  return { leadingPunct, coreWord, trailingPunct };
}

/**
 * Renders a string of text as a list of paragraphs, handling English markers.
 *
 * @param content - The input text string to render.
 * @returns An array of React nodes representing the paragraphs.
 */
export function renderSimpleParagraphs(content: string): React.ReactNode[] {
  return content
    .split("\n\n")
    .map((paragraph, pIndex) => {
      const isEnglish = paragraph.includes(ENGLISH_TEXT_MARKER);
      const text = stripEnglishTextMarkers(paragraph);

      if (!text.trim()) return null; // Don't render empty paragraphs

      return (
        <p
          key={`simple-para-${pIndex}`}
          className={cn(
            "mb-4 whitespace-pre-wrap",
            isEnglish && "text-muted-foreground", // Style entire paragraph if English
          )}
        >
          {text}
        </p>
      );
    })
    .filter(Boolean); // Remove nulls from empty paragraphs
}

/**
 * Strips English text markers from a paragraph.
 *
 * @param text - The input text string to strip markers from.
 * @returns The text with markers stripped, or the original text if no markers are found.
 */
export function stripEnglishTextMarkers(text: string): string {
  const isEnglishParagraph = text.includes("__ENGLISH_TEXT_START__");
  if (!isEnglishParagraph) return text;
  const paragraphText = text
    .replaceAll(ENGLISH_TEXT_MARKER, "")
    .replaceAll(ENGLISH_TEXT_END_MARKER, "");

  return paragraphText;
}

export function addEnglishTextMarkers(text: string): string {
  return `${ENGLISH_TEXT_MARKER}${text}${ENGLISH_TEXT_END_MARKER}`;
}
