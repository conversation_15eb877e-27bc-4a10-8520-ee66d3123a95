import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import {
  saveStory,
  getAllStories,
  deleteStory,
  getStory,
  saveDraft,
  getDraft,
  clearDraft,
  ExtendedStory,
} from '@/lib/db';
import type { Story } from '@/lib/mocks/stories/stories';

export function useIndexedDBStories() {
  const [stories, setStories] = useState<ExtendedStory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [draft, setDraft] = useState<ExtendedStory | null>(null);
  const [isDraftLoading, setIsDraftLoading] = useState(false);

  const loadStories = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const allStories = await getAllStories();
      setStories(allStories);
    } catch (err) {
      console.error('Error loading stories from IndexedDB:', err);
      setError('Failed to load saved stories');
      toast.error('Failed to load saved stories');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const saveStoryToDb = useCallback(
    async (story: Story) => {
      try {
        await saveStory(story);
        toast.success(`Story "${story.title}" saved successfully`);
        await loadStories();
        return true;
      } catch (err) {
        console.error('Error saving story to IndexedDB:', err);
        toast.error(`Failed to save story "${story.title}"`);
        return false;
      }
    },
    [loadStories],
  );

  const deleteStoryFromDb = useCallback(
    async (id: string, title: string) => {
      try {
        await deleteStory(id);
        toast.success(`Story "${title}" deleted`);
        await loadStories();
        return true;
      } catch (err) {
        console.error('Error deleting story from IndexedDB:', err);
        toast.error(`Failed to delete story "${title}"`);
        return false;
      }
    },
    [loadStories],
  );

  const getStoryById = useCallback(
    async (id: string): Promise<ExtendedStory | undefined> => {
      try {
        const story = await getStory(id);
        return story;
      } catch (err) {
        console.error('Error getting story from IndexedDB:', err);
        toast.error('Failed to load story');
        return undefined;
      }
    },
    [],
  );

  const loadDraft = useCallback(async () => {
    setIsDraftLoading(true);
    try {
      const draftData = await getDraft();
      setDraft(draftData || null);
      return draftData || null;
    } catch (err) {
      console.error('Error loading draft from IndexedDB:', err);
      return null;
    } finally {
      setIsDraftLoading(false);
    }
  }, []);

  const saveDraftToDb = useCallback(async (draftData: Partial<ExtendedStory>) => {
    try {
      const currentDraft = (await getDraft()) || { id: 'current-draft' };
      const mergedDraft = { ...currentDraft, ...draftData };

      await saveDraft(mergedDraft);

      setDraft(mergedDraft as ExtendedStory);
      return true;
    } catch (err) {
      console.error('Error saving draft to IndexedDB:', err);
      return false;
    }
  }, []);

  const clearDraftFromDb = useCallback(async () => {
    try {
      await clearDraft();
      setDraft(null);
      return true;
    } catch (err) {
      console.error('Error clearing draft from IndexedDB:', err);
      return false;
    }
  }, []);

  useEffect(() => {
    loadStories();
    loadDraft();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {
    stories,
    isLoading,
    error,
    draft,
    isDraftLoading,
    loadStories,
    loadDraft,
    saveStory: saveStoryToDb,
    saveDraft: saveDraftToDb,
    clearDraft: clearDraftFromDb,
    deleteStory: deleteStoryFromDb,
    getStoryById,
  };
}
