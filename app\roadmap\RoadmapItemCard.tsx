import { Checkbox } from '@/components/ui/checkbox';

export type RoadmapStatus = 'Planned' | 'In progress' | 'Completed';

export interface RoadmapItem {
  id: string;
  title: string;
  status: RoadmapStatus;
  date: string;
  description: string;
}

export function RoadmapItemCard({ id, title, status, description }: RoadmapItem) {
  const isCompleted = status === 'Completed';

  return (
    <div className="flex items-center space-x-3 p-3 bg-background rounded-md border shadow-sm">
      <div className="flex">
        <Checkbox id={`${status.toLowerCase()}-item-${id}`} checked={isCompleted} />
        <div className="ml-2 -mt-0.5">
          <div>
            <label
              htmlFor={`${status.toLowerCase()}-item-${id}`}
              className={`text-sm font-medium leading-none`}
            >
              {title}
            </label>
          </div>
          <p className="text-xs text-muted-foreground font-[400]">{description}</p>
        </div>
      </div>
    </div>
  );
}
