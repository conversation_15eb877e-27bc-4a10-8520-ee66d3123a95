'use client';

import { useState } from 'react';
import { toast } from 'sonner';
import {
  Loader2Icon,
  CopyIcon,
  XIcon,
  DownloadIcon,
  SaveIcon,
  ArrowLeftIcon,
  HeadphonesIcon,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { VOICES_WITH_PREVIEW } from '@/lib/constants';
import type { WordAlignment } from '@/lib/internal-api/types';

interface CreateEditStoryTabProps {
  title: string;
  setTitle: (value: string) => void;
  description: string;
  setDescription: (value: string) => void;
  selectedVoiceId: string;
  setSelectedVoiceId: (value: string) => void;
  inputText: string;
  setInputText: (value: string) => void;
  generatedWords: WordAlignment[] | null;
  setGeneratedWords: (value: WordAlignment[] | null) => void;
  generatedText: string | null;
  setGeneratedText: (value: string | null) => void;
  audioUrl: string | null;
  setAudioUrl: (url: string | null) => void;
  setAudioBlob: (blob: Blob | null) => void;
  editMode: boolean;
  onSaveStory: () => Promise<void>;
  onDownloadJson: () => void;
  onBackToStories: () => void;
}

export default function CreateEditStoryTab({
  title,
  setTitle,
  description,
  setDescription,
  selectedVoiceId,
  setSelectedVoiceId,
  inputText,
  setInputText,
  generatedWords,
  setGeneratedWords,
  generatedText,
  setGeneratedText,
  audioUrl,
  editMode,
  onSaveStory,
  onDownloadJson,
  onBackToStories,
}: CreateEditStoryTabProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [audioFileName, setAudioFileName] = useState<string>('');

  const handleAudioFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setAudioFileName(file.name);
      setAudioBlob(file);
    }
  };

  const voiceOptions = Object.entries(VOICES_WITH_PREVIEW).map(([_name, voiceData]) => ({
    name: voiceData.name,
    id: voiceData.id,
    preview: voiceData.preview,
  }));

  const handleGenerateAudio = async () => {
    if (!inputText.trim()) {
      toast.error('Please enter some text to generate words.');
      return;
    }

    if (!selectedVoiceId) {
      toast.error('Please select a voice.');
      return;
    }

    if (!audioBlob) {
      toast.error('Please upload an audio file.');
      return;
    }

    setIsLoading(true);
    setGeneratedWords(null);
    setGeneratedText(null);
    setError(null);

    try {
      toast.info('Generating TTS alignment data...', { id: 'tts-generate' });

      // Create FormData and append the file and text
      const formData = new FormData();
      formData.append('audio_file', audioBlob);
      formData.append('text', inputText);

      const generateTTSAlignmentResponse = await fetch('/api/tts/generate-words', {
        method: 'POST',
        body: formData,
      });

      if (!generateTTSAlignmentResponse.ok) {
        throw new Error('Failed to generate word alignment');
      }

      const data = await generateTTSAlignmentResponse.json();

      toast.success('TTS alignment data generated.', { id: 'tts-generate' });

      setGeneratedWords(data.words);
      setGeneratedText(data.text);
      toast.success('Word alignment generated successfully!', {
        id: 'tts-convert',
      });
    } catch (error) {
      let errorMessage = 'An unknown error occurred.';
      if (error instanceof Error) {
        errorMessage = error.message;
        console.error('Error generating word alignment:', error);
      } else {
        console.error('Unknown error generating word alignment:', error);
      }

      setError(errorMessage);
      toast.error(errorMessage);
      toast.dismiss('tts-generate');
      toast.dismiss('tts-convert');
      setGeneratedWords(null);
      setGeneratedText(null);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-semibold">
          {editMode ? `Editing: ${title}` : 'Create New Story'}
        </h2>
        <Button variant="outline" onClick={onBackToStories} className="gap-1">
          <ArrowLeftIcon className="h-4 w-4" />
          Back to Stories
        </Button>
      </div>

      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Story Details</CardTitle>
            <CardDescription>Enter the details and content of your story</CardDescription>
          </CardHeader>
          <CardContent className="grid gap-6">
            <div className="grid gap-4">
              <div>
                <Label htmlFor="storyTitle">Title</Label>
                <Input
                  id="storyTitle"
                  value={title}
                  onChange={(event) => setTitle(event.target.value)}
                  placeholder="Enter story title..."
                  className="mt-1"
                  disabled={isLoading}
                />
              </div>
              <div>
                <Label htmlFor="storyDescription">Description</Label>
                <Input
                  id="storyDescription"
                  value={description}
                  onChange={(event) => setDescription(event.target.value)}
                  placeholder="Enter a short description (optional)..."
                  className="mt-1"
                  disabled={isLoading}
                />
              </div>
              <div>
                <Label htmlFor="voiceSelection">Voice</Label>
                <Select
                  value={selectedVoiceId}
                  onValueChange={setSelectedVoiceId}
                  disabled={isLoading}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select a voice" />
                  </SelectTrigger>
                  <SelectContent>
                    {voiceOptions.map((voice) => (
                      <SelectItem key={voice.id} value={voice.id}>
                        {voice.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div>
              <Label htmlFor="storyText">Story Text</Label>
              <div className="relative mt-1">
                <ScrollArea className="h-[400px] w-full rounded-md border">
                  <Textarea
                    id="storyText"
                    value={inputText}
                    onChange={(event) => setInputText(event.target.value)}
                    placeholder="Paste the full story text here..."
                    className="h-full min-h-[400px] w-full resize-none border-0 focus-visible:ring-0 focus-visible:ring-offset-0 p-4"
                    disabled={isLoading}
                  />
                </ScrollArea>
                <p className="text-sm text-muted-foreground text-right mt-1">
                  {inputText.length} characters
                </p>
                {inputText && !isLoading && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute top-2 right-2 h-7 w-7 text-muted-foreground z-10"
                    onClick={() => setInputText('')}
                    title="Clear text"
                  >
                    <XIcon className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
            <div>
              <Label htmlFor="audioFile">Audio File</Label>
              <div className="flex items-center gap-2 mt-1">
                <Input
                  id="audioFile"
                  type="file"
                  accept="audio/*"
                  onChange={handleAudioFileChange}
                  className="flex-1"
                  disabled={isLoading}
                />
                {audioFileName && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-9 w-9"
                    onClick={() => {
                      setAudioFileName('');
                      setAudioBlob(null);
                    }}
                    title="Clear audio file"
                  >
                    <XIcon className="h-4 w-4" />
                  </Button>
                )}
              </div>
              {audioFileName && (
                <p className="text-sm text-muted-foreground mt-1">
                  Selected file: {audioFileName}
                </p>
              )}
            </div>
            {audioUrl && (
              <div>
                <Label>Generated Audio Preview</Label>
                <audio controls src={audioUrl} className="w-full mt-1">
                  Your browser does not support the audio element.
                </audio>
              </div>
            )}
            {error && <p className="text-sm text-destructive">Error: {error}</p>}
            <div className="flex gap-3 justify-end mt-2">
              <Button
                onClick={handleGenerateAudio}
                disabled={
                  isLoading || !inputText.trim() || !selectedVoiceId || !audioBlob
                }
                className="gap-2"
              >
                {isLoading ? (
                  <>
                    <Loader2Icon className="h-4 w-4 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <HeadphonesIcon className="h-4 w-4" />
                    Generate Audio
                  </>
                )}
              </Button>

              <Button
                className="gap-2"
                onClick={onSaveStory}
                disabled={!title.trim()}
                variant={'default'}
              >
                <SaveIcon className="h-4 w-4" />
                Save Story
              </Button>
            </div>
          </CardContent>
        </Card>

        {generatedText && (
          <Card>
            <CardHeader className="flex flex-row items-start justify-between gap-2">
              <div>
                <CardTitle>Generated Output</CardTitle>
                <CardDescription>
                  Review the generated text and word data.
                </CardDescription>
              </div>
              <div className="flex gap-2 flex-shrink-0">
                {generatedText && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="gap-1"
                    onClick={() => {
                      navigator.clipboard.writeText(generatedText);
                      toast.success('Text copied to clipboard!');
                    }}
                  >
                    <CopyIcon className="h-3.5 w-3.5" />
                    Copy Text
                  </Button>
                )}
                {generatedText && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="gap-1"
                    onClick={() => {
                      navigator.clipboard.writeText(
                        JSON.stringify(generatedText, null, 2),
                      );
                      toast.success('JSON copied to clipboard!');
                    }}
                  >
                    <CopyIcon className="h-3.5 w-3.5" />
                    Copy JSON
                  </Button>
                )}
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-1"
                  onClick={onDownloadJson}
                  disabled={!generatedText || !title.trim()}
                >
                  <DownloadIcon className="h-3.5 w-3.5" />
                  Download JSON
                </Button>
              </div>
            </CardHeader>
            <CardContent className="grid gap-4">
              {generatedText && (
                <div>
                  <Label className="text-sm font-medium">Generated Text</Label>
                  <ScrollArea className="h-[200px] w-full rounded-md border mt-1 bg-muted/40">
                    <Textarea
                      readOnly
                      value={generatedText}
                      className="h-full min-h-[200px] w-full resize-none border-0 focus-visible:ring-0 focus-visible:ring-offset-0 p-4 font-mono text-sm"
                    />
                  </ScrollArea>
                </div>
              )}
              {generatedWords && (
                <div>
                  <Label className="text-sm font-medium">Generated Words JSON</Label>
                  <ScrollArea className="h-[200px] w-full rounded-md border mt-1 bg-muted/40">
                    <pre className="text-sm p-4">
                      {JSON.stringify(generatedWords, null, 2)}
                    </pre>
                  </ScrollArea>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </>
  );
}
