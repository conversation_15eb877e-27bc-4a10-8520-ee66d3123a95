{"id": "miesto-lexi", "title": "<PERSON><PERSON><PERSON>", "english_title": "Lexi's Place", "audio_url": "https://x9xfaca7mb.ufs.sh/f/TuGkI0HClmz3kwb3a72XOMTwSXl6IjdExPaWHUmfLR4D9ov5", "image_url": "/images/stories/miesto_lexi.png", "voice_id": "AZKbYmQbJEONExTR4mbE", "description": "Lexi dostane novú izbu, ktorú premení na svoje miesto plné kníh.", "dictionary": [], "text": "Miesto Lexi\n\nLexi bola nesmierne nadšená. Jej rodičia jej povedali, že môže používať malú izbu na konci chodby. „Toto môže byť tvoje špeciálne miesto,\" povedala jej mama - sama autorka, ktor<PERSON> milovala príbehy. Lexi presne vedela, čo chce - izbu plnú kníh.\n\nIzba vyzerala nudne so starými, ž<PERSON><PERSON>mi stenami. Lexi chcela niečo špeciálne. „Chcem modrú,\" povedala otcovi. „Pripomína mi oblohu, keď čítam.\" Otec sa usmial a pomohol jej vymaľovať steny jemnou svetlomodrou. Teraz izba vyzerala ako jej vlastná.\n\nKnižnica bola najdôležitejšou časťou. Jej u<PERSON>, ktor<PERSON> bol vždy ochotný pomôcť, jej vyrobil veľk<PERSON> dreven<PERSON> policu, k<PERSON><PERSON>a celú stenu. „Konečne mám miesto pre všetky moje knihy!\" povedala Lexi s veľkým úsmevom. Opatrne poukladala knihy na políc a usporiadala ich podľa farieb. Dúha knižných chrbtov ju napĺňala hrdosťou.\n\nV rohu pri okne Lexi umiestnila mäkké kreslo s množstvom vankúšov. Toto bolo jej čitateľské miesto. Tu by strávila hodiny čítaním o čarovných svetoch a statočných hrdinoch. Pridala malý stolček na horúcu čokoládu a lampu, ktorá vyzerala ako mesiac. Keď ju večer zapla, izba sa zdala úplne magická.\n\nJej stará mama jej darovala krásny starý koberec so vzorom listov a kvetov. Bol teplý pod jej nohami, keď sa prechádzala a hľadala dokonalú knihu. Nad kreslo zavesila obrázky svojich obľúbených knižných postáv a zošit, kde si občas sama skúšala písať.\n\nKaždý deň po škole Lexi išla do svojej špeciálnej izby. Vybrala si knihu, sadla si do kresla a cestovala do rôznych svetov. „Moja izba je ako dvere do mnohých miest,\" povedala svojej kamarátke Katke, keď ju prišla navštíviť. „Tu môžem ísť kamkoľvek chcem.\"\n\nLexina čitateľská izba nebola len miestnosťou s knihami. Bol to jej vlastný malý svet, kde príbehy oživovali a kde sa cítila naozaj šťastná. Keď zatvorila dvere a otvorila knihu, nič iné už nebolo dôležité. Bol to jej raj.", "translation": "Lexi's Place\n\n<PERSON> was incredibly excited. Her parents told her she could use the small room at the end of the hallway. \"This can be your special place,\" said her mother - an author herself who loved stories. Lexi knew exactly what she wanted - a room full of books.\n\nThe room looked dull with its old, yellow walls. Lexi wanted something special. \"I want blue,\" she told her father. \"It reminds me of the sky when I read.\" Her father smiled and helped her paint the walls a soft light blue. Now the room felt truly hers.\n\nThe bookshelf was the most important part. Her uncle <PERSON><PERSON>, always willing to help, made her a big wooden shelf that covered the entire wall. \"Finally, I have a place for all my books!\" <PERSON> said with a big smile. She carefully arranged the books on the shelf and sorted them by color. The rainbow of book spines filled her with pride.\n\nIn the corner by the window, Lexi placed a soft chair with many pillows. This was her reading spot. Here she would spend hours reading about magical worlds and brave heroes. She added a small table for her hot chocolate and a lamp that looked like the moon. When she turned it on in the evening, the room seemed completely magical.\n\nHer grandmother had given her a beautiful old rug with a pattern of leaves and flowers. It was warm under her feet as she walked around looking for the perfect book. Above the chair, she hung pictures of her favorite book characters and a notebook where she sometimes tried writing herself.\n\nEvery day after school, Lexi went to her special room. She would choose a book, sit in the chair, and travel to different worlds. \"My room is like a door to many places,\" she told her friend <PERSON><PERSON> when she came to visit. \"Here, I can go anywhere I want.\"\n\n<PERSON>'s reading room was more than just a room with books. It was her own small world where stories came alive and where she felt truly happy. When she closed the door and opened a book, nothing else mattered. It was her paradise.", "words": [{"text": "<PERSON><PERSON><PERSON>", "start": 0.099, "end": 0.539}, {"text": " ", "start": 0.539, "end": 0.56}, {"text": "Lexi", "start": 0.56, "end": 1.179}, {"text": "\n", "start": 1.179, "end": 3.74}, {"text": "\n", "start": 3.74, "end": 3.779}, {"text": "Lexi", "start": 3.779, "end": 4.159}, {"text": " ", "start": 4.159, "end": 4.179}, {"text": "bola", "start": 4.179, "end": 4.4}, {"text": " ", "start": 4.4, "end": 4.5}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 4.5, "end": 5.139}, {"text": " ", "start": 5.139, "end": 5.179}, {"text": "nadšená.", "start": 5.179, "end": 5.699}, {"text": " ", "start": 5.699, "end": 6.079}, {"text": "<PERSON><PERSON>", "start": 6.079, "end": 6.179}, {"text": " ", "start": 6.179, "end": 6.259}, {"text": "rod<PERSON>č<PERSON>", "start": 6.259, "end": 6.679}, {"text": " ", "start": 6.679, "end": 6.699}, {"text": "jej", "start": 6.699, "end": 6.819}, {"text": " ", "start": 6.819, "end": 6.899}, {"text": "pove<PERSON><PERSON>,", "start": 6.899, "end": 7.359}, {"text": " ", "start": 7.359, "end": 7.519}, {"text": "že", "start": 7.519, "end": 7.639}, {"text": " ", "start": 7.639, "end": 7.799}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 7.799, "end": 8.119}, {"text": " ", "start": 8.119, "end": 8.159}, {"text": "používať", "start": 8.159, "end": 8.619}, {"text": " ", "start": 8.619, "end": 8.72}, {"text": "malú", "start": 8.72, "end": 9.019}, {"text": " ", "start": 9.019, "end": 9.119}, {"text": "izbu", "start": 9.119, "end": 9.359}, {"text": " ", "start": 9.359, "end": 9.399}, {"text": "na", "start": 9.399, "end": 9.519}, {"text": " ", "start": 9.519, "end": 9.579}, {"text": "konci", "start": 9.579, "end": 9.92}, {"text": " ", "start": 9.92, "end": 9.96}, {"text": "chodby.", "start": 9.96, "end": 10.359}, {"text": " ", "start": 10.359, "end": 11.159}, {"text": "„Toto", "start": 11.159, "end": 11.46}, {"text": " ", "start": 11.46, "end": 11.519}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 11.519, "end": 11.76}, {"text": " ", "start": 11.76, "end": 11.779}, {"text": "byť", "start": 11.779, "end": 11.899}, {"text": " ", "start": 11.899, "end": 11.96}, {"text": "tvoje", "start": 11.96, "end": 12.139}, {"text": " ", "start": 12.139, "end": 12.179}, {"text": "špeciálne", "start": 12.179, "end": 12.84}, {"text": " ", "start": 12.84, "end": 12.92}, {"text": "miesto,\"", "start": 12.92, "end": 13.299}, {"text": " ", "start": 13.299, "end": 13.42}, {"text": "povedala", "start": 13.42, "end": 13.799}, {"text": " ", "start": 13.799, "end": 13.84}, {"text": "jej", "start": 13.84, "end": 13.979}, {"text": " ", "start": 13.979, "end": 14.039}, {"text": "mama", "start": 14.039, "end": 14.479}, {"text": " ", "start": 14.479, "end": 14.519}, {"text": "-", "start": 14.519, "end": 14.52}, {"text": " ", "start": 14.52, "end": 14.599}, {"text": "sama", "start": 14.599, "end": 14.859}, {"text": " ", "start": 14.859, "end": 14.939}, {"text": "autorka,", "start": 14.939, "end": 15.399}, {"text": " ", "start": 15.399, "end": 15.5}, {"text": "ktorá", "start": 15.5, "end": 15.759}, {"text": " ", "start": 15.759, "end": 15.799}, {"text": "milovala", "start": 15.799, "end": 16.26}, {"text": " ", "start": 16.26, "end": 16.299}, {"text": "príbehy.", "start": 16.299, "end": 16.819}, {"text": " ", "start": 16.819, "end": 17.44}, {"text": "Lexi", "start": 17.44, "end": 17.779}, {"text": " ", "start": 17.779, "end": 17.84}, {"text": "presne", "start": 17.84, "end": 18.199}, {"text": " ", "start": 18.199, "end": 18.219}, {"text": "vedela,", "start": 18.219, "end": 18.52}, {"text": " ", "start": 18.52, "end": 18.619}, {"text": "čo", "start": 18.619, "end": 18.68}, {"text": " ", "start": 18.68, "end": 18.699}, {"text": "chce", "start": 18.699, "end": 19.399}, {"text": " ", "start": 19.399, "end": 19.439}, {"text": "-", "start": 19.439, "end": 19.44}, {"text": " ", "start": 19.44, "end": 19.5}, {"text": "izbu", "start": 19.5, "end": 20.26}, {"text": " ", "start": 20.26, "end": 20.319}, {"text": "plnú", "start": 20.319, "end": 20.779}, {"text": " ", "start": 20.779, "end": 20.859}, {"text": "kn<PERSON>h.", "start": 20.859, "end": 21.419}, {"text": "\n", "start": 21.419, "end": 22.399}, {"text": "\n", "start": 22.399, "end": 22.439}, {"text": "Izba", "start": 22.439, "end": 22.699}, {"text": " ", "start": 22.699, "end": 22.719}, {"text": "vyzerala", "start": 22.719, "end": 23.139}, {"text": " ", "start": 23.139, "end": 23.239}, {"text": "nudne", "start": 23.239, "end": 23.699}, {"text": " ", "start": 23.699, "end": 23.739}, {"text": "so", "start": 23.739, "end": 23.859}, {"text": " ", "start": 23.859, "end": 23.939}, {"text": "<PERSON><PERSON><PERSON>,", "start": 23.939, "end": 24.34}, {"text": " ", "start": 24.34, "end": 24.439}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 24.439, "end": 24.859}, {"text": " ", "start": 24.859, "end": 24.92}, {"text": "stenami.", "start": 24.92, "end": 25.459}, {"text": " ", "start": 25.459, "end": 26.279}, {"text": "Lexi", "start": 26.279, "end": 26.559}, {"text": " ", "start": 26.559, "end": 26.579}, {"text": "chcela", "start": 26.579, "end": 26.84}, {"text": " ", "start": 26.84, "end": 26.899}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 26.899, "end": 27.119}, {"text": " ", "start": 27.119, "end": 27.18}, {"text": "špeciálne.", "start": 27.18, "end": 27.92}, {"text": " ", "start": 27.92, "end": 28.539}, {"text": "„Chcem", "start": 28.539, "end": 28.859}, {"text": " ", "start": 28.859, "end": 28.939}, {"text": "mod<PERSON><PERSON>,\"", "start": 28.939, "end": 29.299}, {"text": " ", "start": 29.299, "end": 29.5}, {"text": "povedala", "start": 29.5, "end": 29.879}, {"text": " ", "start": 29.879, "end": 29.92}, {"text": "otcovi.", "start": 29.92, "end": 30.359}, {"text": " ", "start": 30.359, "end": 30.799}, {"text": "„Pripomína", "start": 30.799, "end": 31.399}, {"text": " ", "start": 31.399, "end": 31.459}, {"text": "mi", "start": 31.459, "end": 31.619}, {"text": " ", "start": 31.619, "end": 31.679}, {"text": "oblo<PERSON>,", "start": 31.679, "end": 32.059}, {"text": " ", "start": 32.059, "end": 32.139}, {"text": "keď", "start": 32.139, "end": 32.34}, {"text": " ", "start": 32.34, "end": 32.419}, {"text": "<PERSON><PERSON><PERSON>.\"", "start": 32.419, "end": 32.86}, {"text": " ", "start": 32.86, "end": 33.719}, {"text": "Otec", "start": 33.719, "end": 34.06}, {"text": " ", "start": 34.06, "end": 34.061}, {"text": "sa", "start": 34.061, "end": 34.279}, {"text": " ", "start": 34.279, "end": 34.36}, {"text": "usmial", "start": 34.36, "end": 35.0}, {"text": " ", "start": 35.0, "end": 35.079}, {"text": "a", "start": 35.079, "end": 35.139}, {"text": " ", "start": 35.139, "end": 35.2}, {"text": "pomohol", "start": 35.2, "end": 35.559}, {"text": " ", "start": 35.559, "end": 35.619}, {"text": "jej", "start": 35.619, "end": 35.779}, {"text": " ", "start": 35.779, "end": 35.84}, {"text": "vymaľovať", "start": 35.84, "end": 36.399}, {"text": " ", "start": 36.399, "end": 36.459}, {"text": "steny", "start": 36.459, "end": 36.879}, {"text": " ", "start": 36.879, "end": 37.04}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 37.04, "end": 37.459}, {"text": " ", "start": 37.459, "end": 37.54}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "start": 37.54, "end": 38.279}, {"text": " ", "start": 38.279, "end": 39.36}, {"text": "Teraz", "start": 39.36, "end": 40.299}, {"text": " ", "start": 40.299, "end": 40.36}, {"text": "izba", "start": 40.36, "end": 40.919}, {"text": " ", "start": 40.919, "end": 41.099}, {"text": "vyzerala", "start": 41.099, "end": 41.599}, {"text": " ", "start": 41.599, "end": 41.639}, {"text": "ako", "start": 41.639, "end": 41.86}, {"text": " ", "start": 41.86, "end": 41.879}, {"text": "jej", "start": 41.879, "end": 42.04}, {"text": " ", "start": 42.04, "end": 42.159}, {"text": "vlastná.", "start": 42.159, "end": 43.119}, {"text": "\n", "start": 43.119, "end": 44.18}, {"text": "\n", "start": 44.18, "end": 44.239}, {"text": "Knižnica", "start": 44.239, "end": 44.759}, {"text": " ", "start": 44.759, "end": 44.799}, {"text": "bola", "start": 44.799, "end": 45.0}, {"text": " ", "start": 45.0, "end": 45.099}, {"text": "najd<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 45.099, "end": 45.979}, {"text": " ", "start": 45.979, "end": 46.039}, {"text": "čas<PERSON><PERSON>.", "start": 46.039, "end": 46.479}, {"text": " ", "start": 46.479, "end": 47.079}, {"text": "<PERSON><PERSON>", "start": 47.079, "end": 47.299}, {"text": " ", "start": 47.299, "end": 47.379}, {"text": "ujo", "start": 47.379, "end": 47.699}, {"text": " ", "start": 47.699, "end": 47.739}, {"text": "<PERSON><PERSON><PERSON>,", "start": 47.739, "end": 48.2}, {"text": " ", "start": 48.2, "end": 48.439}, {"text": "<PERSON><PERSON><PERSON>", "start": 48.439, "end": 48.659}, {"text": " ", "start": 48.659, "end": 48.7}, {"text": "bol", "start": 48.7, "end": 48.819}, {"text": " ", "start": 48.819, "end": 48.899}, {"text": "vždy", "start": 48.899, "end": 49.2}, {"text": " ", "start": 49.2, "end": 49.279}, {"text": "ochotný", "start": 49.279, "end": 49.659}, {"text": " ", "start": 49.659, "end": 49.7}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>,", "start": 49.7, "end": 50.18}, {"text": " ", "start": 50.18, "end": 50.5}, {"text": "jej", "start": 50.5, "end": 50.639}, {"text": " ", "start": 50.639, "end": 50.659}, {"text": "vyrobil", "start": 50.659, "end": 51.02}, {"text": " ", "start": 51.02, "end": 51.139}, {"text": "veľkú", "start": 51.139, "end": 51.599}, {"text": " ", "start": 51.599, "end": 51.699}, {"text": "dre<PERSON><PERSON>", "start": 51.699, "end": 52.159}, {"text": " ", "start": 52.159, "end": 52.219}, {"text": "p<PERSON><PERSON>,", "start": 52.219, "end": 52.579}, {"text": " ", "start": 52.579, "end": 52.86}, {"text": "ktorá", "start": 52.86, "end": 53.079}, {"text": " ", "start": 53.079, "end": 53.139}, {"text": "zakr<PERSON><PERSON>a", "start": 53.139, "end": 53.659}, {"text": " ", "start": 53.659, "end": 53.739}, {"text": "celú", "start": 53.739, "end": 53.999}, {"text": " ", "start": 53.999, "end": 54.059}, {"text": "stenu.", "start": 54.059, "end": 54.52}, {"text": " ", "start": 54.52, "end": 55.259}, {"text": "„Konečne", "start": 55.259, "end": 55.879}, {"text": " ", "start": 55.879, "end": 55.939}, {"text": "m<PERSON>m", "start": 55.939, "end": 56.139}, {"text": " ", "start": 56.139, "end": 56.199}, {"text": "miesto", "start": 56.199, "end": 56.719}, {"text": " ", "start": 56.719, "end": 56.759}, {"text": "pre", "start": 56.759, "end": 56.879}, {"text": " ", "start": 56.879, "end": 56.939}, {"text": "všetky", "start": 56.939, "end": 57.399}, {"text": " ", "start": 57.399, "end": 57.459}, {"text": "moje", "start": 57.459, "end": 57.719}, {"text": " ", "start": 57.719, "end": 57.819}, {"text": "knihy!\"", "start": 57.819, "end": 58.199}, {"text": " ", "start": 58.199, "end": 58.619}, {"text": "povedala", "start": 58.619, "end": 59.0}, {"text": " ", "start": 59.0, "end": 59.039}, {"text": "Lexi", "start": 59.039, "end": 59.52}, {"text": " ", "start": 59.52, "end": 59.639}, {"text": "s", "start": 59.639, "end": 59.659}, {"text": " ", "start": 59.659, "end": 59.699}, {"text": "veľkým", "start": 59.699, "end": 60.139}, {"text": " ", "start": 60.139, "end": 60.259}, {"text": "úsmevom.", "start": 60.259, "end": 60.839}, {"text": " ", "start": 60.839, "end": 61.939}, {"text": "Opatrne", "start": 61.939, "end": 62.819}, {"text": " ", "start": 62.819, "end": 62.879}, {"text": "poukla<PERSON>a", "start": 62.879, "end": 63.739}, {"text": " ", "start": 63.739, "end": 63.84}, {"text": "knihy", "start": 63.84, "end": 64.579}, {"text": " ", "start": 64.579, "end": 64.699}, {"text": "na", "start": 64.699, "end": 64.879}, {"text": " ", "start": 64.879, "end": 64.939}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 64.939, "end": 65.68}, {"text": " ", "start": 65.68, "end": 65.76}, {"text": "a", "start": 65.76, "end": 65.9}, {"text": " ", "start": 65.9, "end": 66.0}, {"text": "usporiadala", "start": 66.0, "end": 66.899}, {"text": " ", "start": 66.899, "end": 67.0}, {"text": "ich", "start": 67.0, "end": 67.22}, {"text": " ", "start": 67.22, "end": 67.279}, {"text": "podľa", "start": 67.279, "end": 67.619}, {"text": " ", "start": 67.619, "end": 67.699}, {"text": "<PERSON><PERSON><PERSON>.", "start": 67.699, "end": 68.18}, {"text": " ", "start": 68.18, "end": 69.36}, {"text": "<PERSON><PERSON><PERSON>", "start": 69.36, "end": 69.799}, {"text": " ", "start": 69.799, "end": 69.879}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 69.879, "end": 70.519}, {"text": " ", "start": 70.519, "end": 70.559}, {"text": "chrbtov", "start": 70.559, "end": 71.419}, {"text": " ", "start": 71.419, "end": 71.459}, {"text": "ju", "start": 71.459, "end": 71.599}, {"text": " ", "start": 71.599, "end": 71.699}, {"text": "napĺňala", "start": 71.699, "end": 72.479}, {"text": " ", "start": 72.479, "end": 72.559}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>.", "start": 72.559, "end": 73.239}, {"text": "\n", "start": 73.239, "end": 74.22}, {"text": "\n", "start": 74.22, "end": 74.239}, {"text": "V", "start": 74.239, "end": 74.259}, {"text": " ", "start": 74.259, "end": 74.299}, {"text": "rohu", "start": 74.299, "end": 74.519}, {"text": " ", "start": 74.519, "end": 74.559}, {"text": "pri", "start": 74.559, "end": 74.679}, {"text": " ", "start": 74.679, "end": 74.739}, {"text": "okne", "start": 74.739, "end": 74.959}, {"text": " ", "start": 74.959, "end": 75.0}, {"text": "Lexi", "start": 75.0, "end": 75.699}, {"text": " ", "start": 75.699, "end": 75.819}, {"text": "umiest<PERSON><PERSON>", "start": 75.819, "end": 76.339}, {"text": " ", "start": 76.339, "end": 76.459}, {"text": "mäkké", "start": 76.459, "end": 76.759}, {"text": " ", "start": 76.759, "end": 76.86}, {"text": "k<PERSON><PERSON>", "start": 76.86, "end": 77.22}, {"text": " ", "start": 77.22, "end": 77.259}, {"text": "s", "start": 77.259, "end": 77.319}, {"text": " ", "start": 77.319, "end": 77.379}, {"text": "množstvom", "start": 77.379, "end": 77.819}, {"text": " ", "start": 77.819, "end": 77.86}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>.", "start": 77.86, "end": 78.5}, {"text": " ", "start": 78.5, "end": 79.059}, {"text": "Toto", "start": 79.059, "end": 79.699}, {"text": " ", "start": 79.699, "end": 79.779}, {"text": "bolo", "start": 79.779, "end": 79.939}, {"text": " ", "start": 79.939, "end": 79.979}, {"text": "jej", "start": 79.979, "end": 80.139}, {"text": " ", "start": 80.139, "end": 80.22}, {"text": "čitateľské", "start": 80.22, "end": 80.839}, {"text": " ", "start": 80.839, "end": 80.9}, {"text": "miesto.", "start": 80.9, "end": 81.319}, {"text": " ", "start": 81.319, "end": 82.04}, {"text": "Tu", "start": 82.04, "end": 82.099}, {"text": " ", "start": 82.099, "end": 82.139}, {"text": "by", "start": 82.139, "end": 82.239}, {"text": " ", "start": 82.239, "end": 82.319}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 82.319, "end": 82.72}, {"text": " ", "start": 82.72, "end": 82.779}, {"text": "hodiny", "start": 82.779, "end": 83.099}, {"text": " ", "start": 83.099, "end": 83.18}, {"text": "čítaním", "start": 83.18, "end": 83.599}, {"text": " ", "start": 83.599, "end": 83.639}, {"text": "o", "start": 83.639, "end": 83.759}, {"text": " ", "start": 83.759, "end": 83.86}, {"text": "čarovných", "start": 83.86, "end": 84.259}, {"text": " ", "start": 84.259, "end": 84.339}, {"text": "s<PERSON><PERSON>", "start": 84.339, "end": 84.919}, {"text": " ", "start": 84.919, "end": 84.979}, {"text": "a", "start": 84.979, "end": 85.039}, {"text": " ", "start": 85.039, "end": 85.139}, {"text": "stat<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 85.139, "end": 85.699}, {"text": " ", "start": 85.699, "end": 85.72}, {"text": "hrdi<PERSON><PERSON>.", "start": 85.72, "end": 86.259}, {"text": " ", "start": 86.259, "end": 87.0}, {"text": "P<PERSON><PERSON>", "start": 87.0, "end": 87.4}, {"text": " ", "start": 87.4, "end": 87.479}, {"text": "<PERSON><PERSON><PERSON>", "start": 87.479, "end": 87.739}, {"text": " ", "start": 87.739, "end": 87.819}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 87.819, "end": 88.199}, {"text": " ", "start": 88.199, "end": 88.239}, {"text": "na", "start": 88.239, "end": 88.319}, {"text": " ", "start": 88.319, "end": 88.36}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 88.36, "end": 88.699}, {"text": " ", "start": 88.699, "end": 88.759}, {"text": "čokolá<PERSON>", "start": 88.759, "end": 89.319}, {"text": " ", "start": 89.319, "end": 89.4}, {"text": "a", "start": 89.4, "end": 89.439}, {"text": " ", "start": 89.439, "end": 89.579}, {"text": "lampu,", "start": 89.579, "end": 90.199}, {"text": " ", "start": 90.199, "end": 90.819}, {"text": "ktorá", "start": 90.819, "end": 90.999}, {"text": " ", "start": 90.999, "end": 91.019}, {"text": "vyzerala", "start": 91.019, "end": 91.339}, {"text": " ", "start": 91.339, "end": 91.419}, {"text": "ako", "start": 91.419, "end": 91.839}, {"text": " ", "start": 91.839, "end": 92.059}, {"text": "mesiac.", "start": 92.059, "end": 92.939}, {"text": " ", "start": 92.939, "end": 93.939}, {"text": "Keď", "start": 93.939, "end": 94.099}, {"text": " ", "start": 94.099, "end": 94.119}, {"text": "ju", "start": 94.119, "end": 94.159}, {"text": " ", "start": 94.159, "end": 94.199}, {"text": "<PERSON><PERSON><PERSON>", "start": 94.199, "end": 94.499}, {"text": " ", "start": 94.499, "end": 94.559}, {"text": "zapla,", "start": 94.559, "end": 95.059}, {"text": " ", "start": 95.059, "end": 95.099}, {"text": "izba", "start": 95.099, "end": 95.319}, {"text": " ", "start": 95.319, "end": 95.36}, {"text": "sa", "start": 95.36, "end": 95.439}, {"text": " ", "start": 95.439, "end": 95.479}, {"text": "zdala", "start": 95.479, "end": 95.839}, {"text": " ", "start": 95.839, "end": 95.919}, {"text": "úplne", "start": 95.919, "end": 96.359}, {"text": " ", "start": 96.359, "end": 96.559}, {"text": "magická.", "start": 96.559, "end": 97.319}, {"text": "\n", "start": 97.319, "end": 98.119}, {"text": "\n", "start": 98.119, "end": 98.18}, {"text": "<PERSON><PERSON>", "start": 98.18, "end": 98.319}, {"text": " ", "start": 98.319, "end": 98.4}, {"text": "stará", "start": 98.4, "end": 98.699}, {"text": " ", "start": 98.699, "end": 98.759}, {"text": "mama", "start": 98.759, "end": 99.139}, {"text": " ", "start": 99.139, "end": 99.22}, {"text": "jej", "start": 99.22, "end": 99.36}, {"text": " ", "start": 99.36, "end": 99.419}, {"text": "darovala", "start": 99.419, "end": 99.939}, {"text": " ", "start": 99.939, "end": 100.019}, {"text": "krásny", "start": 100.019, "end": 100.559}, {"text": " ", "start": 100.559, "end": 100.659}, {"text": "<PERSON><PERSON>", "start": 100.659, "end": 100.959}, {"text": " ", "start": 100.959, "end": 101.04}, {"text": "koberec", "start": 101.04, "end": 101.699}, {"text": " ", "start": 101.699, "end": 101.799}, {"text": "so", "start": 101.799, "end": 101.919}, {"text": " ", "start": 101.919, "end": 101.979}, {"text": "vzorom", "start": 101.979, "end": 102.399}, {"text": " ", "start": 102.399, "end": 102.459}, {"text": "listov", "start": 102.459, "end": 102.839}, {"text": " ", "start": 102.839, "end": 102.879}, {"text": "a", "start": 102.879, "end": 102.959}, {"text": " ", "start": 102.959, "end": 103.04}, {"text": "kvetov.", "start": 103.04, "end": 103.5}, {"text": " ", "start": 103.5, "end": 104.439}, {"text": "<PERSON><PERSON>", "start": 104.439, "end": 104.619}, {"text": " ", "start": 104.619, "end": 104.68}, {"text": "teplý", "start": 104.68, "end": 105.219}, {"text": " ", "start": 105.219, "end": 105.279}, {"text": "pod", "start": 105.279, "end": 105.439}, {"text": " ", "start": 105.439, "end": 105.479}, {"text": "jej", "start": 105.479, "end": 105.599}, {"text": " ", "start": 105.599, "end": 105.659}, {"text": "<PERSON><PERSON><PERSON>,", "start": 105.659, "end": 106.119}, {"text": " ", "start": 106.119, "end": 106.159}, {"text": "keď", "start": 106.159, "end": 106.279}, {"text": " ", "start": 106.279, "end": 106.399}, {"text": "sa", "start": 106.399, "end": 106.439}, {"text": " ", "start": 106.439, "end": 106.479}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 106.479, "end": 107.239}, {"text": " ", "start": 107.239, "end": 107.259}, {"text": "a", "start": 107.259, "end": 107.479}, {"text": " ", "start": 107.479, "end": 107.5}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 107.5, "end": 107.899}, {"text": " ", "start": 107.899, "end": 107.919}, {"text": "dokonal<PERSON>", "start": 107.919, "end": 108.559}, {"text": " ", "start": 108.559, "end": 108.659}, {"text": "knihu.", "start": 108.659, "end": 109.879}, {"text": " ", "start": 109.879, "end": 109.959}, {"text": "Nad", "start": 109.959, "end": 110.159}, {"text": " ", "start": 110.159, "end": 110.239}, {"text": "k<PERSON><PERSON>", "start": 110.239, "end": 110.999}, {"text": " ", "start": 110.999, "end": 111.019}, {"text": "zavesila", "start": 111.019, "end": 111.599}, {"text": " ", "start": 111.599, "end": 111.699}, {"text": "o<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 111.699, "end": 112.22}, {"text": " ", "start": 112.22, "end": 112.279}, {"text": "s<PERSON><PERSON><PERSON>", "start": 112.279, "end": 112.599}, {"text": " ", "start": 112.599, "end": 112.699}, {"text": "obľúbený<PERSON>", "start": 112.699, "end": 113.299}, {"text": " ", "start": 113.299, "end": 113.619}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 113.619, "end": 113.939}, {"text": " ", "start": 113.939, "end": 113.959}, {"text": "postáv", "start": 113.959, "end": 114.559}, {"text": " ", "start": 114.559, "end": 114.579}, {"text": "a", "start": 114.579, "end": 114.599}, {"text": " ", "start": 114.599, "end": 115.919}, {"text": "<PERSON><PERSON><PERSON><PERSON>,", "start": 115.919, "end": 116.019}, {"text": " ", "start": 116.019, "end": 116.119}, {"text": "kde", "start": 116.119, "end": 116.279}, {"text": " ", "start": 116.279, "end": 116.339}, {"text": "si", "start": 116.339, "end": 116.419}, {"text": " ", "start": 116.419, "end": 116.459}, {"text": "ob<PERSON><PERSON>", "start": 116.459, "end": 116.799}, {"text": " ", "start": 116.799, "end": 116.879}, {"text": "sama", "start": 116.879, "end": 117.459}, {"text": " ", "start": 117.459, "end": 117.54}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 117.54, "end": 118.159}, {"text": " ", "start": 118.159, "end": 118.219}, {"text": "písať.", "start": 118.219, "end": 118.879}, {"text": "\n", "start": 118.879, "end": 119.959}, {"text": "\n", "start": 119.959, "end": 119.979}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 119.979, "end": 120.199}, {"text": " ", "start": 120.199, "end": 120.279}, {"text": "deň", "start": 120.279, "end": 120.559}, {"text": " ", "start": 120.559, "end": 120.639}, {"text": "po", "start": 120.639, "end": 120.739}, {"text": " ", "start": 120.739, "end": 120.959}, {"text": "škole", "start": 120.959, "end": 121.519}, {"text": " ", "start": 121.519, "end": 122.0}, {"text": "Lexi", "start": 122.0, "end": 122.079}, {"text": " ", "start": 122.079, "end": 122.099}, {"text": "<PERSON><PERSON><PERSON>", "start": 122.099, "end": 122.36}, {"text": " ", "start": 122.36, "end": 122.399}, {"text": "do", "start": 122.399, "end": 122.5}, {"text": " ", "start": 122.5, "end": 122.579}, {"text": "svojej", "start": 122.579, "end": 122.899}, {"text": " ", "start": 122.899, "end": 123.0}, {"text": "špeciálnej", "start": 123.0, "end": 123.919}, {"text": " ", "start": 123.919, "end": 124.019}, {"text": "izby.", "start": 124.019, "end": 124.439}, {"text": " ", "start": 124.439, "end": 125.079}, {"text": "Vy<PERSON>la", "start": 125.079, "end": 125.519}, {"text": " ", "start": 125.519, "end": 125.579}, {"text": "si", "start": 125.579, "end": 125.739}, {"text": " ", "start": 125.739, "end": 126.139}, {"text": "knihu,", "start": 126.139, "end": 126.619}, {"text": " ", "start": 126.619, "end": 126.719}, {"text": "<PERSON>la", "start": 126.719, "end": 127.019}, {"text": " ", "start": 127.019, "end": 127.079}, {"text": "si", "start": 127.079, "end": 127.299}, {"text": " ", "start": 127.299, "end": 127.319}, {"text": "do", "start": 127.319, "end": 127.459}, {"text": " ", "start": 127.459, "end": 127.579}, {"text": "k<PERSON><PERSON>", "start": 127.579, "end": 127.999}, {"text": " ", "start": 127.999, "end": 128.259}, {"text": "a", "start": 128.259, "end": 128.279}, {"text": " ", "start": 128.279, "end": 128.3}, {"text": "cest<PERSON><PERSON>", "start": 128.3, "end": 128.94}, {"text": " ", "start": 128.94, "end": 129.0}, {"text": "do", "start": 129.0, "end": 129.119}, {"text": " ", "start": 129.119, "end": 129.199}, {"text": "rôznych", "start": 129.199, "end": 129.899}, {"text": " ", "start": 129.899, "end": 129.979}, {"text": "<PERSON><PERSON><PERSON>.", "start": 129.979, "end": 130.699}, {"text": " ", "start": 130.699, "end": 131.839}, {"text": "„Moja", "start": 131.839, "end": 132.16}, {"text": " ", "start": 132.16, "end": 132.259}, {"text": "izba", "start": 132.259, "end": 132.979}, {"text": " ", "start": 132.979, "end": 133.0}, {"text": "je", "start": 133.0, "end": 133.139}, {"text": " ", "start": 133.139, "end": 133.16}, {"text": "ako", "start": 133.16, "end": 133.539}, {"text": " ", "start": 133.539, "end": 133.559}, {"text": "dvere", "start": 133.559, "end": 134.419}, {"text": " ", "start": 134.419, "end": 134.44}, {"text": "do", "start": 134.44, "end": 134.559}, {"text": " ", "start": 134.559, "end": 134.8}, {"text": "mnohý<PERSON>", "start": 134.8, "end": 135.339}, {"text": " ", "start": 135.339, "end": 135.899}, {"text": "miest,\"", "start": 135.899, "end": 136.0}, {"text": " ", "start": 136.0, "end": 136.72}, {"text": "povedala", "start": 136.72, "end": 137.179}, {"text": " ", "start": 137.179, "end": 137.259}, {"text": "svojej", "start": 137.259, "end": 137.539}, {"text": " ", "start": 137.539, "end": 137.599}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 137.599, "end": 138.239}, {"text": " ", "start": 138.239, "end": 138.3}, {"text": "<PERSON><PERSON>,", "start": 138.3, "end": 138.939}, {"text": " ", "start": 138.939, "end": 139.099}, {"text": "keď", "start": 139.099, "end": 139.16}, {"text": " ", "start": 139.16, "end": 139.179}, {"text": "ju", "start": 139.179, "end": 139.259}, {"text": " ", "start": 139.259, "end": 139.319}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 139.319, "end": 139.66}, {"text": " ", "start": 139.66, "end": 139.699}, {"text": "navštíviť.", "start": 139.699, "end": 140.439}, {"text": " ", "start": 140.439, "end": 141.259}, {"text": "„Tu", "start": 141.259, "end": 141.52}, {"text": " ", "start": 141.52, "end": 141.599}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 141.599, "end": 142.119}, {"text": " ", "start": 142.119, "end": 142.259}, {"text": "ísť", "start": 142.259, "end": 142.52}, {"text": " ", "start": 142.52, "end": 142.919}, {"text": "kamkoľvek", "start": 142.919, "end": 145.739}, {"text": " ", "start": 145.739, "end": 145.759}, {"text": "chcem.\"", "start": 145.759, "end": 145.86}, {"text": "\n", "start": 145.86, "end": 145.919}, {"text": "\n", "start": 145.919, "end": 145.94}, {"text": "<PERSON><PERSON>", "start": 145.94, "end": 146.399}, {"text": " ", "start": 146.399, "end": 146.459}, {"text": "čitateľská", "start": 146.459, "end": 147.08}, {"text": " ", "start": 147.08, "end": 147.16}, {"text": "izba", "start": 147.16, "end": 147.759}, {"text": " ", "start": 147.759, "end": 147.839}, {"text": "nebola", "start": 147.839, "end": 148.139}, {"text": " ", "start": 148.139, "end": 148.179}, {"text": "len", "start": 148.179, "end": 148.339}, {"text": " ", "start": 148.339, "end": 148.399}, {"text": "miestnosťou", "start": 148.399, "end": 149.08}, {"text": " ", "start": 149.08, "end": 149.179}, {"text": "s", "start": 149.179, "end": 149.199}, {"text": " ", "start": 149.199, "end": 149.279}, {"text": "knihami.", "start": 149.279, "end": 150.519}, {"text": " ", "start": 150.519, "end": 150.559}, {"text": "<PERSON><PERSON>", "start": 150.559, "end": 150.72}, {"text": " ", "start": 150.72, "end": 150.759}, {"text": "to", "start": 150.759, "end": 150.839}, {"text": " ", "start": 150.839, "end": 150.879}, {"text": "jej", "start": 150.879, "end": 151.039}, {"text": " ", "start": 151.039, "end": 151.119}, {"text": "vlastný", "start": 151.119, "end": 151.579}, {"text": " ", "start": 151.579, "end": 151.699}, {"text": "<PERSON><PERSON><PERSON>", "start": 151.699, "end": 152.0}, {"text": " ", "start": 152.0, "end": 152.08}, {"text": "svet,", "start": 152.08, "end": 152.66}, {"text": " ", "start": 152.66, "end": 152.699}, {"text": "kde", "start": 152.699, "end": 152.899}, {"text": " ", "start": 152.899, "end": 152.94}, {"text": "prí<PERSON><PERSON>", "start": 152.94, "end": 153.519}, {"text": " ", "start": 153.519, "end": 153.539}, {"text": "oživovali", "start": 153.539, "end": 154.379}, {"text": " ", "start": 154.379, "end": 154.399}, {"text": "a", "start": 154.399, "end": 154.439}, {"text": " ", "start": 154.439, "end": 154.5}, {"text": "kde", "start": 154.5, "end": 154.599}, {"text": " ", "start": 154.599, "end": 154.66}, {"text": "sa", "start": 154.66, "end": 154.8}, {"text": " ", "start": 154.8, "end": 154.819}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 154.819, "end": 155.139}, {"text": " ", "start": 155.139, "end": 155.199}, {"text": "<PERSON><PERSON><PERSON>", "start": 155.199, "end": 155.599}, {"text": " ", "start": 155.599, "end": 155.779}, {"text": "šťastná.", "start": 155.779, "end": 156.36}, {"text": " ", "start": 156.36, "end": 157.259}, {"text": "Keď", "start": 157.259, "end": 157.379}, {"text": " ", "start": 157.379, "end": 157.44}, {"text": "zatvorila", "start": 157.44, "end": 157.939}, {"text": " ", "start": 157.939, "end": 158.0}, {"text": "dvere", "start": 158.0, "end": 158.399}, {"text": " ", "start": 158.399, "end": 158.419}, {"text": "a", "start": 158.419, "end": 158.5}, {"text": " ", "start": 158.5, "end": 158.58}, {"text": "otvorila", "start": 158.58, "end": 159.059}, {"text": " ", "start": 159.059, "end": 159.139}, {"text": "knihu,", "start": 159.139, "end": 159.759}, {"text": " ", "start": 159.759, "end": 159.979}, {"text": "<PERSON><PERSON>", "start": 159.979, "end": 160.079}, {"text": " ", "start": 160.079, "end": 160.139}, {"text": "iné", "start": 160.139, "end": 160.419}, {"text": " ", "start": 160.419, "end": 160.5}, {"text": "už", "start": 160.5, "end": 160.58}, {"text": " ", "start": 160.58, "end": 160.639}, {"text": "nebolo", "start": 160.639, "end": 160.939}, {"text": " ", "start": 160.939, "end": 160.979}, {"text": "d<PERSON>ležité.", "start": 160.979, "end": 162.319}, {"text": " ", "start": 162.319, "end": 162.399}, {"text": "<PERSON><PERSON>", "start": 162.399, "end": 162.539}, {"text": " ", "start": 162.539, "end": 162.599}, {"text": "to", "start": 162.599, "end": 162.679}, {"text": " ", "start": 162.679, "end": 162.72}, {"text": "jej", "start": 162.72, "end": 162.94}, {"text": " ", "start": 162.94, "end": 162.959}, {"text": "raj.", "start": 162.959, "end": 163.199}]}