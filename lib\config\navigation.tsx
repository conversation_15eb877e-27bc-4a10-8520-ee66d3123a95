import { HomeIcon, MapIcon, BookOpenIcon, GithubIcon } from 'lucide-react';

export interface NavItem {
  href: string;
  label: string;
  icon: React.ReactNode;
  external?: boolean;
}

export const navigationItems: NavItem[] = [
  {
    href: '/',
    label: 'Home',
    icon: <HomeIcon size={18} />,
  },
  {
    href: '/roadmap',
    label: 'Roadmap',
    icon: <MapIcon size={18} />,
  },
  {
    href: '/komiksy',
    label: 'Komiksy',
    icon: <BookOpenIcon size={18} />,
  },
  {
    href: 'https://github.com/Xurify',
    label: 'GitHub',
    icon: <GithubIcon size={18} />,
    external: true,
  },
];
