'use client';

import { useState } from 'react';
import { LayoutWrapper } from '@/components/LayoutWrapper';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  ClockIcon,
  MapIcon,
  UsersIcon,
  ScrollIcon,
  CrownIcon,
  SwordIcon,
  ChurchIcon,
  BookOpenIcon,
  BrainIcon
} from 'lucide-react';

// Historical periods data
const historicalPeriods = [
  {
    id: 'early-slavs',
    title: '<PERSON>r<PERSON><PERSON><PERSON>lov<PERSON>v',
    englishTitle: 'Arrival of Slavs',
    period: '5th-6th century',
    description: 'Príchod slovanských kmeňov na územie dnešného Slovenska',
    englishDescription: 'Arrival of Slavic tribes to the territory of present-day Slovakia',
    icon: <UsersIcon className="h-6 w-6" />,
    color: 'bg-blue-500',
    keyEvents: [
      'Príchod prvých slovanských kmeňov',
      'Osídlenie podunajskej nížiny',
      'Vznik prvých slovanských osád'
    ]
  },
  {
    id: 'samo-empire',
    title: 'Samova ríša',
    englishTitle: 'Samo\'s Empire',
    period: '623-658',
    description: 'Prvý známy štátny útvar na slovenskom území',
    englishDescription: 'First known state formation on Slovak territory',
    icon: <CrownIcon className="h-6 w-6" />,
    color: 'bg-purple-500',
    keyEvents: [
      'Vznik Samovej ríše',
      'Obrana proti Avárom',
      'Prvé slovanské kráľovstvo'
    ]
  },
  {
    id: 'great-moravia',
    title: 'Veľká Morava',
    englishTitle: 'Great Moravia',
    period: '833-907',
    description: 'Zlatý vek slovanskej kultúry a písomnosti',
    englishDescription: 'Golden age of Slavic culture and literacy',
    icon: <ChurchIcon className="h-6 w-6" />,
    color: 'bg-yellow-500',
    keyEvents: [
      'Príchod Cyrila a Metoda',
      'Vznik staroslovienčiny',
      'Rozkvět kresťanstva'
    ]
  },
  {
    id: 'hungarian-kingdom',
    title: 'Uhorské kráľovstvo',
    englishTitle: 'Hungarian Kingdom',
    period: '1000-1918',
    description: 'Slovensko ako súčasť Uhorského kráľovstva',
    englishDescription: 'Slovakia as part of the Hungarian Kingdom',
    icon: <SwordIcon className="h-6 w-6" />,
    color: 'bg-red-500',
    keyEvents: [
      'Začlenenie do Uhorska',
      'Tatársky vpád 1241',
      'Husitské vojny',
      'Osmanské nájazdy'
    ]
  }
];

// Historical figures
const historicalFigures = [
  {
    id: 'cyril-methodius',
    name: 'Cyril a Metod',
    englishName: 'Cyril and Methodius',
    period: '9th century',
    role: 'Vierozvestci a tvorcovia písma',
    englishRole: 'Missionaries and creators of script',
    description: 'Bratia, ktorí priniesli kresťanstvo a písomnosť na Veľkú Moravu',
    achievements: [
      'Vytvorenie glagolského písma',
      'Preklad Biblie do staroslovienčiny',
      'Založenie prvých škôl'
    ]
  },
  {
    id: 'svatopluk',
    name: 'Svätopluk I.',
    englishName: 'Svatopluk I',
    period: '870-894',
    role: 'Kráľ Veľkej Moravy',
    englishRole: 'King of Great Moravia',
    description: 'Najvýznamnejší panovník Veľkej Moravy',
    achievements: [
      'Rozšírenie územia Veľkej Moravy',
      'Posilnenie kresťanstva',
      'Diplomatické kontakty s Byzanciou'
    ]
  }
];

export default function HistoriaPage() {
  const [selectedPeriod, setSelectedPeriod] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('timeline');

  return (
    <LayoutWrapper>
      <div className="max-w-7xl mx-auto pb-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">História Slovenska</h1>
          <p className="text-muted-foreground">
            Spoznajte fascinujúcu históriu Slovenska od príchodu Slovanov po súčasnosť
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="timeline" className="flex items-center gap-2">
              <ClockIcon className="h-4 w-4" />
              Časová os
            </TabsTrigger>
            <TabsTrigger value="figures" className="flex items-center gap-2">
              <UsersIcon className="h-4 w-4" />
              Osobnosti
            </TabsTrigger>
            <TabsTrigger value="map" className="flex items-center gap-2">
              <MapIcon className="h-4 w-4" />
              Mapa
            </TabsTrigger>
          </TabsList>

          <TabsContent value="timeline" className="mt-6">
            <div className="grid gap-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {historicalPeriods.map((period) => (
                  <Card
                    key={period.id}
                    className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
                      selectedPeriod === period.id ? 'ring-2 ring-primary' : ''
                    }`}
                    onClick={() => setSelectedPeriod(selectedPeriod === period.id ? null : period.id)}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-center gap-3 mb-2">
                        <div className={`p-2 rounded-lg ${period.color} text-white`}>
                          {period.icon}
                        </div>
                        <Badge variant="secondary">{period.period}</Badge>
                      </div>
                      <CardTitle className="text-lg">{period.title}</CardTitle>
                      <p className="text-sm text-muted-foreground">{period.englishTitle}</p>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm mb-3">{period.description}</p>
                      <p className="text-xs text-muted-foreground italic">
                        {period.englishDescription}
                      </p>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {selectedPeriod && (
                <Card className="mt-6">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <ScrollIcon className="h-5 w-5" />
                      Kľúčové udalosti
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {(() => {
                      const period = historicalPeriods.find(p => p.id === selectedPeriod);
                      return period ? (
                        <ul className="space-y-2">
                          {period.keyEvents.map((event, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                              <span>{event}</span>
                            </li>
                          ))}
                        </ul>
                      ) : null;
                    })()}
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          <TabsContent value="figures" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {historicalFigures.map((figure) => (
                <Card key={figure.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle>{figure.name}</CardTitle>
                        <p className="text-sm text-muted-foreground">{figure.englishName}</p>
                      </div>
                      <Badge variant="outline">{figure.period}</Badge>
                    </div>
                    <p className="text-sm font-medium text-primary">{figure.role}</p>
                    <p className="text-xs text-muted-foreground italic">{figure.englishRole}</p>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm mb-4">{figure.description}</p>
                    <div>
                      <h4 className="font-medium mb-2">Hlavné úspechy:</h4>
                      <ul className="space-y-1">
                        {figure.achievements.map((achievement, index) => (
                          <li key={index} className="flex items-start gap-2 text-sm">
                            <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0" />
                            <span>{achievement}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="map" className="mt-6">
            <div className="grid gap-6">
              {/* Learning Modes */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MapIcon className="h-5 w-5" />
                    Spôsoby učenia histórie
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Vyberte si spôsob, akým sa chcete učiť slovenskú históriu
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="grid md:grid-cols-2 gap-4">
                    <Card className="cursor-pointer hover:shadow-md transition-shadow">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-lg flex items-center gap-2">
                          <BookOpenIcon className="h-5 w-5" />
                          Historické príbehy
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-muted-foreground mb-3">
                          Čítajte zaujímavé príbehy o kľúčových udalostiach slovenskej histórie
                        </p>
                        <Button asChild className="w-full">
                          <a href="/historia/stories">Čítať príbehy</a>
                        </Button>
                      </CardContent>
                    </Card>

                    <Card className="cursor-pointer hover:shadow-md transition-shadow">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-lg flex items-center gap-2">
                          <ClockIcon className="h-5 w-5" />
                          Interaktívna časová os
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-muted-foreground mb-3">
                          Preskúmajte históriu chronologicky s detailnými informáciami
                        </p>
                        <Button asChild variant="outline" className="w-full">
                          <a href="/historia/timeline">Otvoriť časovú os</a>
                        </Button>
                      </CardContent>
                    </Card>

                    <Card className="cursor-pointer hover:shadow-md transition-shadow">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-lg flex items-center gap-2">
                          <BrainIcon className="h-5 w-5" />
                          Historický kvíz
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-muted-foreground mb-3">
                          Otestujte svoje znalosti slovenskej histórie
                        </p>
                        <Button asChild variant="secondary" className="w-full">
                          <a href="/historia/quiz">Spustiť kvíz</a>
                        </Button>
                      </CardContent>
                    </Card>

                    <Card className="cursor-pointer hover:shadow-md transition-shadow opacity-60">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-lg flex items-center gap-2">
                          <MapIcon className="h-5 w-5" />
                          Historické mapy
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-muted-foreground mb-3">
                          Interaktívne mapy zobrazujúce vývoj slovenského územia
                        </p>
                        <Button disabled className="w-full">
                          Pripravuje sa
                        </Button>
                      </CardContent>
                    </Card>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </LayoutWrapper>
  );
}
