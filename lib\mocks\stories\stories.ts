import katarinaStory from './katarina/katarina.json';
import podHviezdamiBratislavyStory from './pod_hviezdami_bratislavy/pod_hviezdami_bratislavy.json';
import novySvetStory from './novy_svet/novy_svet.json';
import zivotPodZemouStory from './zivot_pod_zemou/zivot_pod_zemou.json';
import lenkinHrncekovyKolacStory from './lenkin_hrncekovy_kolac/lenkin_hrncekovy_kolac.json';
import najlepsiaJarnaPolievkaStory from './najlepsia_jarna_polievka/najlepsia_jarna_polievka.json';
import bublaninaAkoOdBabickyStory from './bublanina_ako_od_babicky/bublanina_ako_od_babicky.json';
import miestoLexiStory from './miesto_lexi/miesto_lexi.json';
import { WordAlignment } from '@/lib/internal-api/types';

export interface DictionaryEntry {
  word: string;
  definition: string;
}

export interface Story {
  id: string;
  title: string;
  englishTitle: string;
  description: string;
  words: WordAlignment[];
  text: string;
  audioUrl: string;
  translation: string;
  imageUrl: string;
  voiceId: string;
  dictionary?: DictionaryEntry[];
}

interface JSONStory
  extends Omit<Story, 'audioUrl' | 'imageUrl' | 'voiceId' | 'englishTitle'> {
  audio_url: string;
  image_url: string;
  voice_id: string;
  english_title: string;
}

const transformJSONStory = (story: JSONStory): Story => {
  return {
    id: story.id,
    englishTitle: story.english_title || story.title,
    title: story.title,
    description: story.description,
    words: story.words,
    text: story.text,
    audioUrl: story.audio_url,
    translation: story.translation,
    imageUrl: story.image_url,
    voiceId: story.voice_id,
    dictionary: story.dictionary || [],
  };
};

export const stories = [
  katarinaStory,
  podHviezdamiBratislavyStory,
  novySvetStory,
  zivotPodZemouStory,
  lenkinHrncekovyKolacStory,
  najlepsiaJarnaPolievkaStory,
  bublaninaAkoOdBabickyStory,
  miestoLexiStory,
].map(transformJSONStory);
