import { CharactersAlignment, WordAlignment } from '@/lib/internal-api/types';

export interface WordsAlignment {
  text: string;
  words: WordAlignment[];
}

const skippedCharacters = ['—', '——'];

/**
 * Converts data from the {@link CharactersAlignment} format (with character-level timing)
 * to a format with a full text string and word-level timing objects.
 * Mimics the structure of Katarina.json.
 *
 * @param data The parsed data from the {@link CharactersAlignment} object.
 * @returns An object containing the full text and an array of word objects with timings.
 */
export function convertCharacterAlignmentToWordAlignment(data: {
  alignment: CharactersAlignment;
}): WordsAlignment {
  if (!data?.alignment) {
    throw new Error("Invalid input data: 'alignment' field is missing.");
  }

  const {
    characters,
    character_start_times_seconds: startTimes,
    character_end_times_seconds: endTimes,
  } = data.alignment;

  if (
    !characters ||
    !startTimes ||
    !endTimes ||
    characters.length !== startTimes.length ||
    characters.length !== endTimes.length
  ) {
    throw new Error(
      'Invalid input data: Character and timing arrays are missing, empty, or mismatched.',
    );
  }

  // 1. Create the full text string
  const fullText = characters.join('');

  // 2. Create the words array
  const words: WordAlignment[] = [];
  let currentWord = '';
  let wordStartTime = -1;
  let wordEndTime = -1;

  for (let i = 0; i < characters.length; i++) {
    const char = characters[i];
    const startTime = startTimes[i];
    const endTime = endTimes[i];

    const isWhitespace = char === ' ' || char === '\\n' || char === '\n'; // Handle escaped and literal newlines

    if (isWhitespace) {
      // Finalize the previous word if there was one
      if (
        currentWord.length > 0 &&
        wordStartTime !== -1 &&
        !skippedCharacters.includes(currentWord)
      ) {
        words.push({
          text: currentWord,
          start: wordStartTime,
          end: wordEndTime, // Use the end time of the last char of the word
        });
      }
      // Add the whitespace character as its own word
      words.push({
        text: char,
        start: startTime,
        end: endTime,
      });
      // Reset for the next word
      currentWord = '';
      wordStartTime = -1;
    } else {
      // Continue building the current word
      if (currentWord.length === 0) {
        wordStartTime = startTime; // Mark the start time of the new word
      }
      currentWord += char;
      wordEndTime = endTime; // Update the end time to the current character's end time
    }
  }

  // Add the last word if the text doesn't end with whitespace
  if (currentWord.length > 0 && wordStartTime !== -1) {
    words.push({
      text: currentWord,
      start: wordStartTime,
      end: wordEndTime,
    });
  }

  return {
    text: fullText.replace(/\\n/g, '\n').replace(/—/g, ''),
    words: words,
  };
}
