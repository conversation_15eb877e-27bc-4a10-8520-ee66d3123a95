import { cn } from '@/lib/utils/strings';

interface LayoutWrapperProps {
  children: React.ReactNode;
  removePadding?: boolean;
}

export function LayoutWrapper(props: LayoutWrapperProps) {
  const { children, removePadding } = props;

  return (
    <div className={cn('pt-[72px] md:pt-24 md:px-8', !removePadding && 'px-4')}>
      <div className={cn('max-w-7xl mx-auto', !removePadding && 'py-6')}>{children}</div>
    </div>
  );
}
