'use client';

import React, { useEffect, useState } from 'react';
import {
  PlayIcon,
  PauseIcon,
  Volume2Icon,
  HighlighterIcon,
  RotateCcwIcon,
  RotateCwIcon,
  SettingsIcon,
  TypeIcon,
  RepeatIcon,
  LanguagesIcon,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from '@/components/ui/drawer';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface MobileControlsProps {
  isPlaying: boolean;
  isReady: boolean;
  audioUrl: string;
  currentTime: number;
  duration: number;
  volume: number;
  playbackRate: number;
  toggle: () => void;
  seek: (time: number) => void;
  setVolume: (volume: number) => void;
  setPlaybackRate: (rate: number) => void;
  handleRewind: () => void;
  handleForward: () => void;
  fontSize: number;
  setFontSize: (size: number | ((prev: number) => number)) => void;
  highlightingEnabled: boolean;
  setHighlightingEnabled: (enabled: boolean) => void;
  showTranslation: boolean;
  setShowTranslation: (show: boolean) => void;
  setIsLooping: (enabled: boolean) => void;
  isLooping?: boolean;
  AVAILABLE_RATES: number[];
  REWIND_TIME: number;
}

export function MobileControls({
  isPlaying,
  isReady,
  audioUrl,
  currentTime,
  duration,
  volume,
  playbackRate,
  toggle,
  seek,
  setVolume,
  setPlaybackRate,
  handleRewind,
  handleForward,
  fontSize,
  setFontSize,
  highlightingEnabled,
  setHighlightingEnabled,
  showTranslation,
  setShowTranslation,
  setIsLooping,
  isLooping = false,
  AVAILABLE_RATES: availableRates,
  REWIND_TIME: rewindTime,
}: MobileControlsProps) {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  const sliderValue = isReady ? (currentTime / duration) * 100 : 0;
  const displayTime = (sliderValue / 100) * duration;

  useEffect(() => {
    if (isDrawerOpen) {
      window.history.pushState(null, '', window.location.href);
    }
    const handlePopState = () => {
      if (isDrawerOpen) {
        setIsDrawerOpen(false);
      }
    };

    window.addEventListener('popstate', handlePopState);

    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, [isDrawerOpen]);

  const formatTime = (time: number) => {
    if (isNaN(time) || time < 0) return '0:00';
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
  };

  const handleSliderValueChange = (value: number[]) => {
    const newTime = (value[0] / 100) * duration;
    seek(newTime);
  };

  const handleVolumeChange = (value: number[]) => {
    setVolume(value[0] / 100);
  };

  const handleRateChange = (rateString: string) => {
    if (!isReady) return;
    const newRate = parseFloat(rateString);
    if (!isNaN(newRate)) {
      setPlaybackRate(newRate);
    }
  };

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-md px-4 h-[80px] flex flex-col justify-center border-t border-border/50">
      <div className="space-y-2">
        <Slider
          value={[sliderValue]}
          defaultValue={[sliderValue]}
          onClick={(e) => {
            e.stopPropagation();
          }}
          min={0}
          max={100}
          step={0.1}
          onValueChange={handleSliderValueChange}
          className="w-full h-1.5 cursor-pointer"
          aria-label="Audio playback position"
          disabled={!isReady}
        />
        <div className="grid grid-cols-3 items-center">
          <div className="flex items-center justify-start">
            <div className="text-xs font-mono tabular-nums">
              {formatTime(displayTime)} / {formatTime(duration)}
            </div>
          </div>

          <div className="flex justify-center items-center gap-2">
            <Button
              onClick={handleRewind}
              variant="ghost"
              size="icon"
              className="h-10 w-10 relative"
              disabled={!audioUrl || !isReady}
              aria-label={`Rewind ${rewindTime} seconds`}
            >
              <RotateCcwIcon size={25} />
              <span className="absolute inset-0 flex items-center justify-center text-[11px] font-bold leading-none">
                {rewindTime}
              </span>
            </Button>

            <Button
              onClick={toggle}
              variant="ghost"
              size="icon"
              className="h-9 w-9 rounded flex items-center justify-center"
              disabled={!audioUrl || !isReady}
              aria-label={isPlaying ? 'Pause' : 'Play'}
            >
              {isPlaying ? (
                <PauseIcon fill="currentColor" size={22} />
              ) : (
                <PlayIcon fill="currentColor" size={22} />
              )}
            </Button>

            <Button
              onClick={handleForward}
              variant="ghost"
              size="icon"
              className="h-10 w-10 relative"
              disabled={!audioUrl || !isReady}
              aria-label={`Forward ${rewindTime} seconds`}
            >
              <RotateCwIcon size={25} />
              <span className="absolute inset-0 flex items-center justify-center text-[11px] font-bold leading-none">
                {rewindTime}
              </span>
            </Button>
          </div>

          <div className="flex items-center justify-end">
            <Drawer open={isDrawerOpen} onOpenChange={setIsDrawerOpen}>
              <DrawerTrigger asChild>
                <Button variant="ghost" size="icon" className="h-9 w-9">
                  <SettingsIcon size={18} />
                </Button>
              </DrawerTrigger>
              <DrawerContent>
                <DrawerHeader>
                  <DrawerTitle>Audio & Text Settings</DrawerTitle>
                  <DrawerDescription>
                    Adjust playback and text display settings
                  </DrawerDescription>
                </DrawerHeader>

                <div className="px-4 py-2">
                  <div className="space-y-6">
                    <div className="space-y-4">
                      <h3 className="text-sm font-medium">Audio Controls</h3>

                      <div className="flex items-center justify-center gap-4">
                        <Button
                          onClick={handleRewind}
                          variant="ghost"
                          className="h-12 w-12 p-0 relative"
                          disabled={!audioUrl || !isReady}
                          aria-label={`Rewind ${rewindTime} seconds`}
                        >
                          <RotateCcwIcon size={24} />
                          <span className="absolute inset-0 flex items-center justify-center text-[10px] font-bold leading-none">
                            {rewindTime}
                          </span>
                        </Button>

                        <Button
                          onClick={toggle}
                          variant="default"
                          size="lg"
                          className="flex flex-1 items-center justify-center gap-2 h-12"
                          disabled={!audioUrl || !isReady}
                        >
                          {isPlaying ? (
                            <PauseIcon size={20} />
                          ) : (
                            <PlayIcon size={20} className="ml-0.5" />
                          )}
                          <span>{isPlaying ? 'Pause' : 'Play'}</span>
                        </Button>

                        <Button
                          onClick={handleForward}
                          variant="ghost"
                          className="h-12 w-12 p-0 relative"
                          disabled={!audioUrl || !isReady}
                          aria-label={`Forward ${rewindTime} seconds`}
                        >
                          <RotateCwIcon size={24} />
                          <span className="absolute inset-0 flex items-center justify-center text-[10px] font-bold leading-none">
                            {rewindTime}
                          </span>
                        </Button>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <Label htmlFor="playback-position">Playback Position</Label>
                          <span className="text-xs font-mono">
                            {formatTime(displayTime)} / {formatTime(duration)}
                          </span>
                        </div>
                        <Slider
                          id="playback-position"
                          value={[sliderValue]}
                          defaultValue={[sliderValue]}
                          min={0}
                          max={100}
                          step={0.1}
                          onValueChange={handleSliderValueChange}
                          className="w-full"
                          aria-label="Audio playback position"
                          disabled={!isReady}
                        />
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <Label
                            htmlFor="volume-control"
                            className="flex items-center gap-2"
                          >
                            <Volume2Icon size={16} />
                            <span>Volume</span>
                          </Label>
                          <span className="text-xs">{Math.round(volume * 100)}%</span>
                        </div>
                        <Slider
                          id="volume-control"
                          value={[volume * 100]}
                          onValueChange={handleVolumeChange}
                          min={0}
                          max={100}
                          step={1}
                          className="w-full"
                          aria-label="Audio volume"
                          disabled={!audioUrl}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="playback-speed">Playback Speed</Label>
                        <Select
                          name="playback-speed"
                          value={playbackRate.toString()}
                          onValueChange={handleRateChange}
                          disabled={!audioUrl || !isReady}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Speed" />
                          </SelectTrigger>
                          <SelectContent>
                            {availableRates.map((rate: number) => (
                              <SelectItem key={rate} value={rate.toString()}>
                                {rate}x
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h3 className="text-sm font-medium">Text Display</h3>

                      <div className="flex items-center justify-between">
                        <Label htmlFor="font-size" className="flex items-center gap-2">
                          <TypeIcon size={16} />
                          <span>Font Size: {fontSize}px</span>
                        </Label>
                        <div className="flex items-center gap-1">
                          <Button
                            onClick={() => setFontSize((prev) => Math.max(10, prev - 1))}
                            variant="outline"
                            size="icon"
                            className="h-8 w-8"
                          >
                            <span className="text-xs">A-</span>
                          </Button>
                          <Button
                            onClick={() => setFontSize((prev) => prev + 1)}
                            variant="outline"
                            size="icon"
                            className="h-8 w-8"
                          >
                            <span className="text-base">A+</span>
                          </Button>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <HighlighterIcon size={16} />
                          <Label htmlFor="highlighting-toggle">Word Highlighting</Label>
                        </div>
                        <Switch
                          id="highlighting-toggle"
                          checked={highlightingEnabled}
                          onCheckedChange={setHighlightingEnabled}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <LanguagesIcon size={16} />

                          <Label htmlFor="translation-toggle">Show Translation</Label>
                        </div>
                        <Switch
                          id="translation-toggle"
                          checked={showTranslation}
                          onCheckedChange={setShowTranslation}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <RepeatIcon size={16} />
                          <Label htmlFor="loop-toggle">Loop Audio</Label>
                        </div>
                        <Switch
                          id="loop-toggle"
                          checked={isLooping}
                          onCheckedChange={setIsLooping}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <DrawerFooter>
                  <DrawerClose asChild>
                    <Button variant="outline">Close</Button>
                  </DrawerClose>
                </DrawerFooter>
              </DrawerContent>
            </Drawer>
          </div>
        </div>
      </div>
    </div>
  );
}
