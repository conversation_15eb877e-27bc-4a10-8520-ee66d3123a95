'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { toast } from 'sonner';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { toKebabCase, toSnakeCase } from '@/lib/utils/strings';
import type { WordAlignment } from '@/lib/internal-api/types';
import type { ExtendedStory } from '@/lib/db';
import JsonEditor from '@/components/studio/JsonEditor';
import SavedStoriesTab from '@/components/studio/SavedStoriesTab';
import CreateEditStoryTab from '@/components/studio/CreateEditStoryTab';
import { useIndexedDBStories } from '@/lib/hooks/useIndexedDBStories';

export default function StudioPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const tabParam = searchParams.get('tab');
  const storyIdParam = searchParams.get('storyId');

  const activeTab =
    tabParam && ['saved', 'create', 'timestamp-editor'].includes(tabParam)
      ? tabParam
      : 'saved';

  const setActiveTab = (tab: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('tab', tab);

    if (tab !== 'create') {
      params.delete('storyId');
    }

    router.push(`/studio?${params.toString()}`, { scroll: false });
  };

  const [editMode, setEditMode] = useState(false);
  const [currentStoryId, setCurrentStoryId] = useState<string | null>(null);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const { saveStory, draft, saveDraft, clearDraft, getStoryById } = useIndexedDBStories();

  const [inputText, setInputTextState] = useState(draft?.inputText || '');
  const [title, setTitleState] = useState(draft?.title || '');
  const [description, setDescriptionState] = useState(draft?.description || '');

  useEffect(() => {
    if (draft) {
      if (draft.inputText !== undefined) setInputTextState(draft.inputText);
      if (draft.title !== undefined) setTitleState(draft.title);
      if (draft.description !== undefined) setDescriptionState(draft.description);
    }
  }, [draft]);

  const setInputText = (value: string) => {
    setInputTextState(value);
    saveDraft({ inputText: value });
  };

  const setTitle = (value: string) => {
    setTitleState(value);
    saveDraft({ title: value });
  };

  const setDescription = (value: string) => {
    setDescriptionState(value);
    saveDraft({ description: value });
  };

  const setSelectedVoiceId = (value: string) => {
    saveDraft({ voiceId: value });
  };

  const setGeneratedWords = (value: WordAlignment[] | null) => {
    saveDraft({ words: value || [] });
  };

  const setGeneratedText = (value: string | null) => {
    saveDraft({ text: value || '' });
  };

  const selectedVoiceId = draft?.voiceId || '';
  const generatedWords = draft?.words?.length ? draft.words : null;
  const generatedText = draft?.text || null;

  useEffect(() => {
    const loadStoryFromUrl = async () => {
      if (storyIdParam) {
        const story = await getStoryById(storyIdParam);
        if (story) {
          saveDraft(story);

          setEditMode(true);
          setCurrentStoryId(story.id);

          setInputTextState(story.inputText || '');
          setTitleState(story.title || '');
          setDescriptionState(story.description || '');

          if (activeTab !== 'create') {
            setActiveTab('create');
          }
        } else {
          const params = new URLSearchParams(searchParams.toString());
          params.delete('storyId');
          router.replace(`/studio?${params.toString()}`, { scroll: false });
          toast.error('Story not found');
        }
      }
    };

    loadStoryFromUrl();
  }, [
    storyIdParam,
    getStoryById,
    saveDraft,
    activeTab,
    router,
    searchParams,
    setInputTextState,
    setTitleState,
    setDescriptionState,
  ]);

  useEffect(() => {
    return () => {
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, [audioUrl]);

  const createStoryObject = (): ExtendedStory | null => {
    if (!draft) return null;

    return {
      ...draft,
      audioUrl: audioUrl || '',
      id: toKebabCase(draft.title?.trim() || 'untitled'),
      title: draft.title?.trim() || 'Untitled',
      description: draft.description?.trim() || '',
    };
  };

  const handleDownloadJson = () => {
    const storyData = createStoryObject();

    if (!storyData) {
      toast.error('Please generate data and provide a title before downloading.');
      return;
    }

    const jsonString = JSON.stringify(storyData, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;

    const filename = `${toSnakeCase(storyData.title) || 'story'}.json`;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    toast.success(`Story JSON downloaded as ${filename}`);
  };

  const handleSaveStory = async () => {
    const storyData = createStoryObject();

    if (!storyData) {
      toast.error('Please provide a title before saving.');
      return;
    }

    if (editMode && currentStoryId) {
      storyData.id = currentStoryId;
    }

    await saveStory(storyData);
    handleClearStory();
  };

  const handleLoadStory = (story: ExtendedStory) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('tab', 'create');
    params.set('storyId', story.id);
    router.push(`/studio?${params.toString()}`, { scroll: false });

    saveDraft(story);

    setEditMode(true);
    setCurrentStoryId(story.id);
    setActiveTab('create');

    setInputTextState(story.inputText || '');
    setTitleState(story.title || '');
    setDescriptionState(story.description || '');

    toast.success(`Loaded story "${story.title}" for editing`);
  };

  const handleClearStory = () => {
    clearDraft();

    setEditMode(false);
    setCurrentStoryId(null);
    setInputTextState('');
    setTitleState('');
    setDescriptionState('');

    if (storyIdParam) {
      const params = new URLSearchParams(searchParams.toString());
      params.delete('storyId');
      router.replace(`/studio?${params.toString()}`, { scroll: false });
    }
  };

  const handleNewStory = () => {
    handleClearStory();
    setActiveTab('create');
  };

  return (
    <div className="container mx-auto py-10 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6 text-center">Story Studio</h1>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="saved">Saved Stories</TabsTrigger>
          <TabsTrigger value="create">Create/Edit Story</TabsTrigger>
          <TabsTrigger value="timestamp-editor">Timestamp Editor</TabsTrigger>
        </TabsList>

        <TabsContent value="saved">
          <SavedStoriesTab onNewStory={handleNewStory} onLoadStory={handleLoadStory} />
        </TabsContent>

        <TabsContent value="create">
          <CreateEditStoryTab
            title={title}
            setTitle={setTitle}
            description={description}
            setDescription={setDescription}
            selectedVoiceId={selectedVoiceId}
            setSelectedVoiceId={setSelectedVoiceId}
            inputText={inputText}
            setInputText={setInputText}
            generatedWords={generatedWords}
            setGeneratedWords={setGeneratedWords}
            generatedText={generatedText}
            setGeneratedText={setGeneratedText}
            audioUrl={audioUrl}
            audioBlob={audioBlob}
            setAudioUrl={setAudioUrl}
            setAudioBlob={setAudioBlob}
            editMode={editMode}
            onSaveStory={handleSaveStory}
            onDownloadJson={handleDownloadJson}
            onBackToStories={() => setActiveTab('saved')}
          />
        </TabsContent>

        <TabsContent value="timestamp-editor">
          <JsonEditor />
        </TabsContent>
      </Tabs>
    </div>
  );
}
