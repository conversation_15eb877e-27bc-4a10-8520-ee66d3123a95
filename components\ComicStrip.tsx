'use client';

import Image from 'next/image';
import { useState, useEffect } from 'react';
import { Maximize2Icon } from 'lucide-react';
import { cn } from '@/lib/utils/strings';
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogTrigger,
  DialogHeader,
  DialogDescription,
} from '@/components/ui/dialog';

export interface ComicStripProps {
  imageUrl: string;
  title?: string;
  altText?: string;
  className?: string;
}

// Helper component for displaying image error messages
function ImageErrorMessage({
  imageUrl,
  isSmall = false,
}: {
  imageUrl: string;
  isSmall?: boolean;
}) {
  return (
    <div
      className={`absolute inset-0 flex items-center justify-center bg-muted/20 text-black ${isSmall ? 'text-xs' : ''}`}
    >
      <p className={isSmall ? 'text-center p-2' : ''}>
        {isSmall ? 'Image not found' : `Failed to load image: ${imageUrl}`}
      </p>
    </div>
  );
}

export function ComicStrip({ imageUrl, title, altText, className }: ComicStripProps) {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [thumbnailError, setThumbnailError] = useState(false);
  const [modalImageError, setModalImageError] = useState(false);

  useEffect(() => {
    if (isFullscreen) {
      setModalImageError(false);
    }
  }, [isFullscreen, imageUrl]);

  return (
    <div className={cn('', className)}>
      <Dialog open={isFullscreen} onOpenChange={setIsFullscreen}>
        <DialogTrigger asChild>
          <div className="cursor-pointer group relative">
            <div className="relative aspect-square w-full">
              <Image
                src={imageUrl}
                alt={altText || title || 'Comic strip'}
                fill
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                className="object-cover transition-transform duration-300 group-hover:scale-105"
                onError={() => setThumbnailError(true)}
              />
              {thumbnailError && <ImageErrorMessage imageUrl={imageUrl} isSmall={true} />}
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-opacity duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                <Maximize2Icon className="h-8 w-8 text-white drop-shadow-md" />
              </div>
            </div>
          </div>
        </DialogTrigger>
        <DialogContent
          className="max-w-[95vw] p-0 bg-transparent border-0 shadow-none"
          enableOverlayButton={true}
        >
          <DialogHeader>
            <DialogTitle>{''}</DialogTitle>
            <DialogDescription>{''}</DialogDescription>
          </DialogHeader>
          <div className="flex items-center justify-center h-full w-full">
            <Image
              src={imageUrl}
              alt={altText || title || 'Comic strip'}
              className="max-h-full max-w-full object-contain"
              onError={() => setModalImageError(true)}
              width={500}
              height={750}
            />
            {modalImageError && <ImageErrorMessage imageUrl={imageUrl} />}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
