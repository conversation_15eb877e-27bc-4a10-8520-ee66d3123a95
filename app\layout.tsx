import { Suspense } from 'react';
import type { Metadata, Viewport } from 'next';
import { cookies } from 'next/headers';
import { <PERSON><PERSON><PERSON>, PT_Sans } from 'next/font/google';
import { Analytics } from '@vercel/analytics/react';
import { PostHogProvider } from '@/app/providers/PostHogProvider';
import { Theme, ThemeProvider } from '@/app/providers/ThemeProvider';
import { DeviceProvider } from '@/app/providers/DeviceProvider';
import { Toaster } from '@/components/ui/sonner';
import { ClientLayout } from '@/components/ClientLayout';
import { getServerDeviceInfo } from '@/lib/utils/device';
import './globals.css';

const nunito = Nunito({
  variable: '--font-nunito',
  subsets: ['latin'],
});

const ptSans = PT_Sans({
  variable: '--font-pt-sans',
  subsets: ['latin'],
  weight: ['400', '700'],
});

export const metadata: Metadata = {
  title: 'Osem Jeden - Slovak Language Learning',
  description:
    'Learn Slovak through short engaging stories. Improve your comprehension, listening skills, and enhance your language learning experience.',
  metadataBase: new URL('https://beta.osemjeden.com'),
  alternates: {
    canonical: 'https://beta.osemjeden.com',
  },
  applicationName: 'Osem Jeden',
  keywords: [
    'Slovak language',
    'language learning',
    'audio synchronization',
    'interactive learning',
    'pronunciation practice',
    'Slovak pronunciation',
  ],
  authors: [{ name: 'Xurify' }],
  creator: 'Xurify',
  publisher: 'Osem Jeden',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  openGraph: {
    title: 'Osem Jeden - Slovak Language Learning',
    description:
      'Learn Slovak through short interactive stories. Improve comprehension, listening skills, and enhance your language learning experience.',
    type: 'website',
    locale: 'en_US',
    siteName: 'Osem Jeden',
    url: 'https://beta.osemjeden.com',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Osem Jeden - Slovak Language Learning',
    description:
      'Learn Slovak through short interactive stories. Improve comprehension, listening skills, and enhance your language learning experience.',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  // verification: {
  //   google: 'google-site-verification-code', // Replace with actual verification code
  // },
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  // themeColor: [
  //   { media: '(prefers-color-scheme: light)', color: '#E0CDAB' },
  //   { media: '(prefers-color-scheme: dark)', color: '#2A3626' },
  // ],
};

export default async function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const cookieStore = await cookies();
  const themeCookie = cookieStore.get('theme');
  const theme = (themeCookie?.value || 'light') as Theme | undefined;

  let themeClass = 'dark';
  if (theme === 'dark') {
    themeClass = 'dark';
  } else if (theme === 'light') {
    themeClass = 'light';
  }

  const deviceInfo = await getServerDeviceInfo();

  return (
    <html lang="en" className={themeClass}>
      <link
        rel="icon"
        href="/images/icons/osemjeden.png"
        type="image/png"
        sizes="1024x1024"
      />
      <head>
        {themeCookie?.value ? (
          <meta
            name="theme-color"
            content={themeCookie.value === 'dark' ? '#2A3626' : '#E0CDAB'}
          />
        ) : (
          <>
            <meta
              name="theme-color"
              content="#E0CDAB"
              media="(prefers-color-scheme: light)"
            />
            <meta
              name="theme-color"
              content="#2A3626"
              media="(prefers-color-scheme: dark)"
            />
          </>
        )}
      </head>
      <DeviceProvider initialDeviceState={deviceInfo}>
        <ThemeProvider defaultTheme={theme}>
          <body className={`${nunito.variable} ${ptSans.variable} antialiased relative`}>
            <PostHogProvider>
              <div className="texture" />
              <ClientLayout>{children}</ClientLayout>
              <Toaster />
            </PostHogProvider>
            <Suspense>
              <Analytics />
            </Suspense>
          </body>
        </ThemeProvider>
      </DeviceProvider>
    </html>
  );
}
