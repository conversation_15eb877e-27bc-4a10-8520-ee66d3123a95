import { openDB, DBSchema, IDBPDatabase } from 'idb';
import type { Story } from '@/lib/mocks/stories/stories';

export interface ExtendedStory extends Story {
  inputText?: string;
  dateModified?: Date;
}

interface StoryDB extends DBSchema {
  stories: {
    key: string;
    value: ExtendedStory;
    indexes: {
      'by-title': string;
      'by-date': Date;
    };
  };
  drafts: {
    key: string;
    value: ExtendedStory;
  };
}

const DB_NAME = 'osemjeden';
const DB_VERSION = 1;

let dbPromise: Promise<IDBPDatabase<StoryDB>> | null = null;

export function initDB(): Promise<IDBPDatabase<StoryDB>> {
  if (!dbPromise) {
    dbPromise = openDB<StoryDB>(DB_NAME, DB_VERSION, {
      upgrade(db, oldVersion) {
        if (!db.objectStoreNames.contains('stories')) {
          const storyStore = db.createObjectStore('stories', { keyPath: 'id' });
          storyStore.createIndex('by-title', 'title');
          storyStore.createIndex('by-date', 'dateModified');
        }

        if (oldVersion < 2 && !db.objectStoreNames.contains('drafts')) {
          db.createObjectStore('drafts', { keyPath: 'id' });
        }
      },
    });
  }

  return dbPromise;
}

export async function saveStory(story: Story | ExtendedStory): Promise<string> {
  const db = await initDB();
  const storyToSave: ExtendedStory = {
    ...story,
    dateModified: new Date(),
  };

  await db.put('stories', storyToSave);
  return story.id;
}

export async function getStory(id: string): Promise<ExtendedStory | undefined> {
  const db = await initDB();
  return db.get('stories', id);
}

export async function getAllStories(): Promise<ExtendedStory[]> {
  const db = await initDB();
  return db.getAll('stories');
}

export async function deleteStory(id: string): Promise<void> {
  const db = await initDB();
  await db.delete('stories', id);
}

export async function clearStories(): Promise<void> {
  const db = await initDB();
  await db.clear('stories');
}

export async function saveDraft(draft: Partial<ExtendedStory>): Promise<void> {
  const db = await initDB();

  let existingDraft: ExtendedStory | undefined;
  try {
    existingDraft = await db.get('drafts', 'current-draft');
  } catch (error) {
    console.error('Error getting existing draft', error);
  }

  const draftToSave: ExtendedStory = {
    id: 'current-draft',
    title: draft.title !== undefined ? draft.title : existingDraft?.title || '',
    englishTitle:
      draft.englishTitle !== undefined
        ? draft.englishTitle
        : existingDraft?.englishTitle || '',
    audioUrl:
      draft.audioUrl !== undefined ? draft.audioUrl : existingDraft?.audioUrl || '',
    imageUrl:
      draft.imageUrl !== undefined ? draft.imageUrl : existingDraft?.imageUrl || '',
    voiceId: draft.voiceId !== undefined ? draft.voiceId : existingDraft?.voiceId || '',
    description:
      draft.description !== undefined
        ? draft.description
        : existingDraft?.description || '',
    text: draft.text !== undefined ? draft.text : existingDraft?.text || '',
    translation:
      draft.translation !== undefined
        ? draft.translation
        : existingDraft?.translation || '',
    words: draft.words !== undefined ? draft.words : existingDraft?.words || [],
    inputText:
      draft.inputText !== undefined ? draft.inputText : existingDraft?.inputText || '',
    dateModified: new Date(),
  };

  await db.put('drafts', draftToSave);
}

export async function getDraft(): Promise<ExtendedStory | undefined> {
  const db = await initDB();
  return db.get('drafts', 'current-draft');
}

export async function clearDraft(): Promise<void> {
  const db = await initDB();
  try {
    await db.delete('drafts', 'current-draft');
  } catch (error) {
    console.error('Error clearing draft', error);
  }
}
