import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Truncates a string to a maximum length
 * @param text - The string to truncate
 * @param maxLength - The maximum length of the string
 * @returns The truncated string
 */
export function truncate(text: string, maxLength: number = 20) {
  if (!text) return '';
  if (text.length <= maxLength) {
    return text;
  }
  return text.slice(0, maxLength).trim() + '...';
}

/**
 * Converts a base64 string to a Blob
 * @param base64 - The base64 string to convert
 * @param contentType - The content type of the blob
 * @param sliceSize - The size of the slice to use for the blob
 * @returns A Blob object
 */
export function base64ToBlob(base64: string, contentType = '', sliceSize = 512) {
  const byteCharacters = atob(base64);
  const byteArrays = [];

  for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
    const slice = byteCharacters.slice(offset, offset + sliceSize);

    const byteNumbers = new Array(slice.length);
    for (let i = 0; i < slice.length; i++) {
      byteNumbers[i] = slice.charCodeAt(i);
    }

    const byteArray = new Uint8Array(byteNumbers);
    byteArrays.push(byteArray);
  }

  return new Blob(byteArrays, { type: contentType });
}

/**
 * Converts a string to kebab-case.
 * Handles various separators like spaces, underscores, and camelCase.
 *
 * @param input - The string to convert.
 * @returns The kebab-cased string.
 */
export const toKebabCase = (input: string | null | undefined): string => {
  if (!input) {
    return '';
  }

  return (
    input
      // Match uppercase letters preceded by lowercase/numbers (insert hyphen)
      // e.g., "camelCase" -> "camel-Case"
      .replace(/([a-z0-9])([A-Z])/g, '$1-$2')
      // Match uppercase letters preceded by other uppercase letters if followed by lowercase (insert hyphen)
      // e.g., "SOMEWord" -> "SOME-Word"
      .replace(/([A-Z])([A-Z])(?=[a-z])/g, '$1-$2')
      // Replace spaces and underscores with hyphens
      .replace(/[\s_]+/g, '-')
      // Convert to lowercase
      .toLowerCase()
      // Remove any potential leading/trailing hyphens (e.g., from initial spaces or multiple hyphens)
      .replace(/^-+|-+$/g, '')
  );
};

export const toSnakeCase = (input: string | null | undefined): string => {
  if (!input) {
    return '';
  }
  return input.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
};

/**
 * Strips punctuation from a string.
 * @param text - The string to strip punctuation from.
 * @returns The string with punctuation removed.
 */
export const stripPunctuation = (text: string): string =>
  text.replace(/[.,;:!?"'()\[\]{}]/g, '');
