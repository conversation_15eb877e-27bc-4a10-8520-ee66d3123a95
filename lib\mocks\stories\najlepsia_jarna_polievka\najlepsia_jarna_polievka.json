{"id": "najlepsia-jarna-polievka", "title": "Najlepšia jarná polievka", "english_title": "The Best Spring Soup", "audio_url": "https://x9xfaca7mb.ufs.sh/f/TuGkI0HClmz3ecCRyeUnJBsumNEHaczx1k9j3rSY6lpvR2fF", "image_url": "/images/stories/najlepsia_jarna_polievka.png", "voice_id": "Y7LuuXmOcq2mxQM3UPYq", "description": "Katarína sa rozhodne variť špargľovú polievku a jej priateľ Alex je nadšený z jedla.", "dictionary": [{"word": "polievka", "definition": "soup"}, {"word": "špargľa", "definition": "asparagus"}, {"word": "jar", "definition": "spring", "type": "noun"}, {"word": "jar<PERSON>", "definition": "spring", "type": "adjective"}, {"word": "variť", "definition": "to cook", "type": "verb"}, {"word": "<PERSON><PERSON><PERSON><PERSON>", "definition": "vegetables"}, {"word": "recept", "definition": "recipe"}, {"word": "trh", "definition": "market"}, {"word": "čerstvá", "definition": "fresh"}, {"word": "zemiaky", "definition": "potatoes"}], "text": "Najlepšia jarná polievka\n\nAhoj! Tu Katarína!\n\nKonečne som späť na Slovensku. Je to už druhýkrát. Mám veľmi rada jar. Prečo? Pretože jar na Slovensku je veľmi výnimočná a krásna. A tiež rastie moja obľúbená zelenina - špargľa.\n\nVonku je krásny jarný deň. Pozerám z okna. Neskôr príde môj priateľ <PERSON>, ale neviem, čo budem dnes variť.\n\n„Aha, už viem! Uvarím špargľovú polievku!”\n\nJoj! Čas je 12:00. Musím ísť na trh. Poďme!\n\nNa trhu je veľa farebnej zeleniny. <PERSON>ele<PERSON>, oran<PERSON>ová, červená. Všetko je také krásne.\n\nNo, kúpme si len špargľu. Zelená a čerstvá.\n\n„<PERSON><PERSON><PERSON><PERSON> de<PERSON>, prosím kilo špargle!\", povedal som.\n\n„<PERSON> má<PERSON>.” povedala pani s úsmevom.\n\nĎakujem!\n\nUž som doma. Poďme variť.\n\nNajprv dáme do hrnca trochu olivového oleja a cibuľu.\n\nPotom umyjeme špargľu a nakrájame ju na malé kúsky.\n\nKeď je cibuľu mäkká, pridáme špargľu a zemiaky a všetko spolu varíme 15 minút.\n\nMmm, už to vonia! Teraz pridáme trochu smotany na varenie a potom to všetko zamiešame.\n\nTakmer hotovo! Na záver posypeme soľou, korením a trochou parmezánu.\n\nAlex je tu! Alex prichádza a hovorí: „To vyzerá skvele! Čo to je?”\n\n„To je špargľova polievku”, povedala som.\n\nPo jedle je Alex veľmi spokojný a hovorí… „Mmm. Musíš mi dať tento recept! Je to naozaj tá najlepšia jarná polievka!”", "translation": "The Best Spring Soup\n\nHello! <PERSON><PERSON> here!\n\nI'm finally back in Slovakia. It's the second time. I like spring very much. Why? Because spring in Slovakia is very special and beautiful. And also my favourite vegetable - asparagus - is growing.\n\nIt is a beautiful spring day outside. I look out of the window. Later my friend <PERSON> is coming, but I don't know what I'm going to cook today.\n\n\"Oh, I already know! I'll cook asparagus soup!\"\n\nYay! The time is 12:00. I have to go to the market. Let's go!\n\nThere are lots of colorful vegetables at the market. Green, orange, red. Everything is so beautiful.\n\nWell, let's just buy asparagus. Green and fresh.\n\n\"Hello, a kilo of asparagus please!\", I said.\n\n\"Here you go.\" Said the lady with a smile.\n\nThank you!\n\nI'm home now. Let's cook.\n\nFirst we put some olive oil and onions in the pot.\n\nThen we wash the asparagus and cut it into small pieces.\n\nWhen the onion is soft, add the asparagus and potatoes and cook everything together for 15 minutes.\n\nMmm, it smells good already! Now add a little cooking cream and then stir it all up.\n\nAlmost done! Finally, sprinkle with salt, pepper and a little parmesan cheese.\n\n<PERSON> is here! <PERSON> comes in and says: \"This looks great! What is it?\"\n\n\"It's asparagus soup,\" I said.\n\nAfter eating, <PERSON> is very satisfied and says... \"Mmm. You have to give me this recipe! It really is the best spring soup ever!\"", "words": [{"text": "Najlepšia", "start": 0.099, "end": 0.779}, {"text": " ", "start": 0.779, "end": 0.799}, {"text": "jar<PERSON>", "start": 0.799, "end": 1.159}, {"text": " ", "start": 1.159, "end": 1.319}, {"text": "polievka", "start": 1.319, "end": 1.899}, {"text": "\n", "start": 1.899, "end": 4.139}, {"text": "\n", "start": 4.139, "end": 4.179}, {"text": "Ahoj!", "start": 4.179, "end": 4.5}, {"text": " ", "start": 4.5, "end": 4.579}, {"text": "Tu", "start": 4.579, "end": 4.639}, {"text": " ", "start": 4.639, "end": 4.739}, {"text": "Katarína!", "start": 4.739, "end": 5.359}, {"text": "\n", "start": 5.359, "end": 5.639}, {"text": "\n", "start": 5.639, "end": 5.679}, {"text": "<PERSON><PERSON>č<PERSON>", "start": 5.679, "end": 6.179}, {"text": " ", "start": 6.179, "end": 6.219}, {"text": "som", "start": 6.219, "end": 6.319}, {"text": " ", "start": 6.319, "end": 6.379}, {"text": "späť", "start": 6.379, "end": 6.619}, {"text": " ", "start": 6.619, "end": 6.659}, {"text": "na", "start": 6.659, "end": 6.799}, {"text": " ", "start": 6.799, "end": 6.859}, {"text": "Slovensku.", "start": 6.859, "end": 7.359}, {"text": " ", "start": 7.359, "end": 7.699}, {"text": "Je", "start": 7.699, "end": 7.759}, {"text": " ", "start": 7.759, "end": 7.819}, {"text": "to", "start": 7.819, "end": 7.899}, {"text": " ", "start": 7.899, "end": 7.919}, {"text": "už", "start": 7.919, "end": 8.039}, {"text": " ", "start": 8.039, "end": 8.139}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "start": 8.139, "end": 8.679}, {"text": " ", "start": 8.679, "end": 8.979}, {"text": "<PERSON><PERSON><PERSON>", "start": 8.979, "end": 9.119}, {"text": " ", "start": 9.119, "end": 9.179}, {"text": "<PERSON><PERSON><PERSON>", "start": 9.179, "end": 9.42}, {"text": " ", "start": 9.42, "end": 9.46}, {"text": "rada", "start": 9.46, "end": 9.659}, {"text": " ", "start": 9.659, "end": 9.719}, {"text": "jar.", "start": 9.719, "end": 9.899}, {"text": " ", "start": 9.899, "end": 10.319}, {"text": "Prečo?", "start": 10.319, "end": 10.699}, {"text": " ", "start": 10.699, "end": 11.119}, {"text": "Pretože", "start": 11.119, "end": 11.519}, {"text": " ", "start": 11.519, "end": 11.539}, {"text": "jar", "start": 11.539, "end": 11.739}, {"text": " ", "start": 11.739, "end": 11.8}, {"text": "na", "start": 11.8, "end": 11.92}, {"text": " ", "start": 11.92, "end": 12.0}, {"text": "Slovensku", "start": 12.0, "end": 12.619}, {"text": " ", "start": 12.619, "end": 12.699}, {"text": "je", "start": 12.699, "end": 12.779}, {"text": " ", "start": 12.779, "end": 12.819}, {"text": "<PERSON><PERSON><PERSON>", "start": 12.819, "end": 13.139}, {"text": " ", "start": 13.139, "end": 13.179}, {"text": "výnimočná", "start": 13.179, "end": 13.839}, {"text": " ", "start": 13.839, "end": 13.899}, {"text": "a", "start": 13.899, "end": 13.979}, {"text": " ", "start": 13.979, "end": 14.039}, {"text": "krásna.", "start": 14.039, "end": 14.579}, {"text": " ", "start": 14.579, "end": 14.96}, {"text": "A", "start": 14.96, "end": 15.019}, {"text": " ", "start": 15.019, "end": 15.059}, {"text": "tiež", "start": 15.059, "end": 15.279}, {"text": " ", "start": 15.279, "end": 15.319}, {"text": "rastie", "start": 15.319, "end": 15.619}, {"text": " ", "start": 15.619, "end": 15.659}, {"text": "moja", "start": 15.659, "end": 15.859}, {"text": " ", "start": 15.859, "end": 15.92}, {"text": "obľúbená", "start": 15.92, "end": 16.34}, {"text": " ", "start": 16.34, "end": 16.44}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 16.44, "end": 17.1}, {"text": " ", "start": 17.1, "end": 17.119}, {"text": "-", "start": 17.119, "end": 17.12}, {"text": " ", "start": 17.12, "end": 17.18}, {"text": "špargľa.", "start": 17.18, "end": 17.659}, {"text": "\n", "start": 17.659, "end": 18.359}, {"text": "\n", "start": 18.359, "end": 18.42}, {"text": "<PERSON><PERSON>", "start": 18.42, "end": 18.68}, {"text": " ", "start": 18.68, "end": 18.719}, {"text": "je", "start": 18.719, "end": 18.84}, {"text": " ", "start": 18.84, "end": 18.859}, {"text": "krásny", "start": 18.859, "end": 19.379}, {"text": " ", "start": 19.379, "end": 19.42}, {"text": "<PERSON><PERSON><PERSON>", "start": 19.42, "end": 19.719}, {"text": " ", "start": 19.719, "end": 19.779}, {"text": "deň.", "start": 19.779, "end": 20.02}, {"text": " ", "start": 20.02, "end": 20.199}, {"text": "Pozerám", "start": 20.199, "end": 20.539}, {"text": " ", "start": 20.539, "end": 20.619}, {"text": "z", "start": 20.619, "end": 20.639}, {"text": " ", "start": 20.639, "end": 20.739}, {"text": "okna.", "start": 20.739, "end": 21.119}, {"text": " ", "start": 21.119, "end": 21.699}, {"text": "Neskôr", "start": 21.699, "end": 22.219}, {"text": " ", "start": 22.219, "end": 22.26}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 22.26, "end": 22.519}, {"text": " ", "start": 22.519, "end": 22.579}, {"text": "m<PERSON><PERSON>", "start": 22.579, "end": 22.739}, {"text": " ", "start": 22.739, "end": 22.779}, {"text": "pria<PERSON>ľ", "start": 22.779, "end": 23.219}, {"text": " ", "start": 23.219, "end": 23.34}, {"text": "<PERSON>,", "start": 23.34, "end": 23.68}, {"text": " ", "start": 23.68, "end": 23.859}, {"text": "ale", "start": 23.859, "end": 24.0}, {"text": " ", "start": 24.0, "end": 24.059}, {"text": "neviem,", "start": 24.059, "end": 24.319}, {"text": " ", "start": 24.319, "end": 24.399}, {"text": "čo", "start": 24.399, "end": 24.479}, {"text": " ", "start": 24.479, "end": 24.5}, {"text": "budem", "start": 24.5, "end": 24.739}, {"text": " ", "start": 24.739, "end": 24.779}, {"text": "dnes", "start": 24.779, "end": 24.979}, {"text": " ", "start": 24.979, "end": 25.059}, {"text": "variť.", "start": 25.059, "end": 25.399}, {"text": "\n", "start": 25.399, "end": 26.159}, {"text": "\n", "start": 26.159, "end": 26.18}, {"text": "„Aha,", "start": 26.18, "end": 26.42}, {"text": " ", "start": 26.42, "end": 26.619}, {"text": "už", "start": 26.619, "end": 26.739}, {"text": " ", "start": 26.739, "end": 26.779}, {"text": "viem!", "start": 26.779, "end": 27.219}, {"text": " ", "start": 27.219, "end": 27.279}, {"text": "Uvarím", "start": 27.279, "end": 27.639}, {"text": " ", "start": 27.639, "end": 27.699}, {"text": "špargľovú", "start": 27.699, "end": 28.26}, {"text": " ", "start": 28.26, "end": 28.319}, {"text": "polievku!”", "start": 28.319, "end": 29.579}, {"text": "\n", "start": 29.579, "end": 29.619}, {"text": "\n", "start": 29.619, "end": 29.699}, {"text": "Joj!", "start": 29.699, "end": 30.079}, {"text": " ", "start": 30.079, "end": 30.379}, {"text": "Čas", "start": 30.379, "end": 30.579}, {"text": " ", "start": 30.579, "end": 30.619}, {"text": "je", "start": 30.619, "end": 30.699}, {"text": " ", "start": 30.699, "end": 30.92}, {"text": "12:00.", "start": 30.92, "end": 32.039}, {"text": " ", "start": 32.039, "end": 32.139}, {"text": "Musím", "start": 32.139, "end": 32.54}, {"text": " ", "start": 32.54, "end": 32.619}, {"text": "ísť", "start": 32.619, "end": 32.739}, {"text": " ", "start": 32.739, "end": 32.84}, {"text": "na", "start": 32.84, "end": 32.959}, {"text": " ", "start": 32.959, "end": 33.02}, {"text": "trh.", "start": 33.02, "end": 33.299}, {"text": " ", "start": 33.299, "end": 33.459}, {"text": "Poďme!", "start": 33.459, "end": 33.919}, {"text": "\n", "start": 33.919, "end": 34.52}, {"text": "\n", "start": 34.52, "end": 34.559}, {"text": "Na", "start": 34.559, "end": 34.7}, {"text": " ", "start": 34.7, "end": 34.759}, {"text": "trhu", "start": 34.759, "end": 34.979}, {"text": " ", "start": 34.979, "end": 35.02}, {"text": "je", "start": 35.02, "end": 35.139}, {"text": " ", "start": 35.139, "end": 35.2}, {"text": "<PERSON><PERSON><PERSON>", "start": 35.2, "end": 35.479}, {"text": " ", "start": 35.479, "end": 35.52}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 35.52, "end": 35.939}, {"text": " ", "start": 35.939, "end": 36.059}, {"text": "zeleniny.", "start": 36.059, "end": 36.559}, {"text": " ", "start": 36.559, "end": 36.86}, {"text": "Zelená,", "start": 36.86, "end": 37.259}, {"text": " ", "start": 37.259, "end": 37.459}, {"text": "<PERSON><PERSON><PERSON><PERSON>,", "start": 37.459, "end": 37.959}, {"text": " ", "start": 37.959, "end": 38.139}, {"text": "červená.", "start": 38.139, "end": 38.599}, {"text": " ", "start": 38.599, "end": 38.799}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 38.799, "end": 39.099}, {"text": " ", "start": 39.099, "end": 39.119}, {"text": "je", "start": 39.119, "end": 39.2}, {"text": " ", "start": 39.2, "end": 39.239}, {"text": "také", "start": 39.239, "end": 39.479}, {"text": " ", "start": 39.479, "end": 39.559}, {"text": "krásne.", "start": 39.559, "end": 40.119}, {"text": "\n", "start": 40.119, "end": 40.759}, {"text": "\n", "start": 40.759, "end": 40.799}, {"text": "No,", "start": 40.799, "end": 41.439}, {"text": " ", "start": 41.439, "end": 41.5}, {"text": "k<PERSON><PERSON>e", "start": 41.5, "end": 41.779}, {"text": " ", "start": 41.779, "end": 41.84}, {"text": "si", "start": 41.84, "end": 41.899}, {"text": " ", "start": 41.899, "end": 41.939}, {"text": "len", "start": 41.939, "end": 42.099}, {"text": " ", "start": 42.099, "end": 42.159}, {"text": "špargľu.", "start": 42.159, "end": 42.619}, {"text": " ", "start": 42.619, "end": 42.819}, {"text": "Zelená", "start": 42.819, "end": 43.259}, {"text": " ", "start": 43.259, "end": 43.299}, {"text": "a", "start": 43.299, "end": 43.399}, {"text": " ", "start": 43.399, "end": 43.479}, {"text": "čerstvá.", "start": 43.479, "end": 44.0}, {"text": "\n", "start": 44.0, "end": 44.739}, {"text": "\n", "start": 44.739, "end": 44.759}, {"text": "„Dobrý", "start": 44.759, "end": 45.099}, {"text": " ", "start": 45.099, "end": 45.139}, {"text": "<PERSON><PERSON>,", "start": 45.139, "end": 45.36}, {"text": " ", "start": 45.36, "end": 45.439}, {"text": "prosím", "start": 45.439, "end": 45.779}, {"text": " ", "start": 45.779, "end": 45.84}, {"text": "kilo", "start": 45.84, "end": 46.059}, {"text": " ", "start": 46.059, "end": 46.119}, {"text": "špargle!\",", "start": 46.119, "end": 46.639}, {"text": " ", "start": 46.639, "end": 47.02}, {"text": "povedal", "start": 47.02, "end": 47.419}, {"text": " ", "start": 47.419, "end": 47.5}, {"text": "som.", "start": 47.5, "end": 47.719}, {"text": "\n", "start": 47.719, "end": 48.34}, {"text": "\n", "start": 48.34, "end": 48.36}, {"text": "„Tu", "start": 48.36, "end": 48.479}, {"text": " ", "start": 48.479, "end": 48.52}, {"text": "m<PERSON><PERSON>.”", "start": 48.52, "end": 49.36}, {"text": " ", "start": 49.36, "end": 49.379}, {"text": "povedala", "start": 49.379, "end": 49.86}, {"text": " ", "start": 49.86, "end": 49.919}, {"text": "pani", "start": 49.919, "end": 50.119}, {"text": " ", "start": 50.119, "end": 50.159}, {"text": "s", "start": 50.159, "end": 50.2}, {"text": " ", "start": 50.2, "end": 50.279}, {"text": "úsmevom.", "start": 50.279, "end": 50.759}, {"text": "\n", "start": 50.759, "end": 51.02}, {"text": "\n", "start": 51.02, "end": 51.219}, {"text": "Ďakujem!", "start": 51.219, "end": 51.739}, {"text": "\n", "start": 51.739, "end": 52.079}, {"text": "\n", "start": 52.079, "end": 52.119}, {"text": "Už", "start": 52.119, "end": 52.199}, {"text": " ", "start": 52.199, "end": 52.259}, {"text": "som", "start": 52.259, "end": 52.399}, {"text": " ", "start": 52.399, "end": 52.439}, {"text": "doma.", "start": 52.439, "end": 52.779}, {"text": " ", "start": 52.779, "end": 53.099}, {"text": "Poďme", "start": 53.099, "end": 53.379}, {"text": " ", "start": 53.379, "end": 53.419}, {"text": "variť.", "start": 53.419, "end": 54.379}, {"text": "\n", "start": 54.379, "end": 54.419}, {"text": "\n", "start": 54.419, "end": 54.459}, {"text": "Najprv", "start": 54.459, "end": 54.919}, {"text": " ", "start": 54.919, "end": 54.979}, {"text": "<PERSON><PERSON><PERSON>", "start": 54.979, "end": 55.239}, {"text": " ", "start": 55.239, "end": 55.299}, {"text": "do", "start": 55.299, "end": 55.399}, {"text": " ", "start": 55.399, "end": 55.439}, {"text": "hrnca", "start": 55.439, "end": 55.819}, {"text": " ", "start": 55.819, "end": 55.879}, {"text": "trochu", "start": 55.879, "end": 56.279}, {"text": " ", "start": 56.279, "end": 56.36}, {"text": "olivové<PERSON>", "start": 56.36, "end": 56.979}, {"text": " ", "start": 56.979, "end": 57.079}, {"text": "<PERSON><PERSON><PERSON>", "start": 57.079, "end": 57.559}, {"text": " ", "start": 57.559, "end": 57.619}, {"text": "a", "start": 57.619, "end": 57.699}, {"text": " ", "start": 57.699, "end": 57.819}, {"text": "cibuľu.", "start": 57.819, "end": 58.319}, {"text": "\n", "start": 58.319, "end": 58.879}, {"text": "\n", "start": 58.879, "end": 58.919}, {"text": "<PERSON><PERSON>", "start": 58.919, "end": 59.239}, {"text": " ", "start": 59.239, "end": 59.299}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 59.299, "end": 59.639}, {"text": " ", "start": 59.639, "end": 59.719}, {"text": "špargľu", "start": 59.719, "end": 60.259}, {"text": " ", "start": 60.259, "end": 60.319}, {"text": "a", "start": 60.319, "end": 60.359}, {"text": " ", "start": 60.359, "end": 60.419}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 60.419, "end": 60.959}, {"text": " ", "start": 60.959, "end": 60.979}, {"text": "ju", "start": 60.979, "end": 61.059}, {"text": " ", "start": 61.059, "end": 61.099}, {"text": "na", "start": 61.099, "end": 61.18}, {"text": " ", "start": 61.18, "end": 61.219}, {"text": "malé", "start": 61.219, "end": 61.479}, {"text": " ", "start": 61.479, "end": 61.539}, {"text": "<PERSON><PERSON><PERSON>.", "start": 61.539, "end": 62.0}, {"text": "\n", "start": 62.0, "end": 62.619}, {"text": "\n", "start": 62.619, "end": 62.639}, {"text": "Keď", "start": 62.639, "end": 62.779}, {"text": " ", "start": 62.779, "end": 62.84}, {"text": "je", "start": 62.84, "end": 62.899}, {"text": " ", "start": 62.899, "end": 63.0}, {"text": "cibuľ<PERSON>", "start": 63.0, "end": 63.399}, {"text": " ", "start": 63.399, "end": 63.459}, {"text": "mäkk<PERSON>,", "start": 63.459, "end": 64.079}, {"text": " ", "start": 64.079, "end": 64.139}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 64.139, "end": 64.559}, {"text": " ", "start": 64.559, "end": 64.739}, {"text": "špargľu", "start": 64.739, "end": 65.159}, {"text": " ", "start": 65.159, "end": 65.22}, {"text": "a", "start": 65.22, "end": 65.299}, {"text": " ", "start": 65.299, "end": 65.36}, {"text": "zemiaky", "start": 65.36, "end": 66.0}, {"text": " ", "start": 66.0, "end": 66.099}, {"text": "a", "start": 66.099, "end": 66.159}, {"text": " ", "start": 66.159, "end": 66.199}, {"text": "všetko", "start": 66.199, "end": 66.699}, {"text": " ", "start": 66.699, "end": 66.739}, {"text": "spolu", "start": 66.739, "end": 67.039}, {"text": " ", "start": 67.039, "end": 67.099}, {"text": "var<PERSON><PERSON>", "start": 67.099, "end": 67.54}, {"text": " ", "start": 67.54, "end": 67.58}, {"text": "15", "start": 67.58, "end": 68.119}, {"text": " ", "start": 68.119, "end": 68.159}, {"text": "minút.", "start": 68.159, "end": 68.659}, {"text": "\n", "start": 68.659, "end": 69.279}, {"text": "\n", "start": 69.279, "end": 69.5}, {"text": "Mmm,", "start": 69.5, "end": 70.079}, {"text": " ", "start": 70.079, "end": 70.119}, {"text": "už", "start": 70.119, "end": 70.239}, {"text": " ", "start": 70.239, "end": 70.319}, {"text": "to", "start": 70.319, "end": 70.4}, {"text": " ", "start": 70.4, "end": 70.459}, {"text": "vonia!", "start": 70.459, "end": 71.379}, {"text": " ", "start": 71.379, "end": 71.439}, {"text": "Teraz", "start": 71.439, "end": 71.699}, {"text": " ", "start": 71.699, "end": 71.739}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 71.739, "end": 72.119}, {"text": " ", "start": 72.119, "end": 72.18}, {"text": "trochu", "start": 72.18, "end": 72.419}, {"text": " ", "start": 72.419, "end": 72.5}, {"text": "smotany", "start": 72.5, "end": 72.919}, {"text": " ", "start": 72.919, "end": 72.959}, {"text": "na", "start": 72.959, "end": 73.04}, {"text": " ", "start": 73.04, "end": 73.099}, {"text": "varenie", "start": 73.099, "end": 73.499}, {"text": " ", "start": 73.499, "end": 73.559}, {"text": "a", "start": 73.559, "end": 73.619}, {"text": " ", "start": 73.619, "end": 73.659}, {"text": "potom", "start": 73.659, "end": 73.9}, {"text": " ", "start": 73.9, "end": 73.959}, {"text": "to", "start": 73.959, "end": 74.019}, {"text": " ", "start": 74.019, "end": 74.059}, {"text": "všetko", "start": 74.059, "end": 74.419}, {"text": " ", "start": 74.419, "end": 74.459}, {"text": "zamiešame.", "start": 74.459, "end": 75.22}, {"text": "\n", "start": 75.22, "end": 75.439}, {"text": "\n", "start": 75.439, "end": 75.479}, {"text": "Takmer", "start": 75.479, "end": 75.819}, {"text": " ", "start": 75.819, "end": 75.879}, {"text": "hotovo!", "start": 75.879, "end": 76.379}, {"text": " ", "start": 76.379, "end": 77.019}, {"text": "Na", "start": 77.019, "end": 77.139}, {"text": " ", "start": 77.139, "end": 77.199}, {"text": "<PERSON><PERSON><PERSON>", "start": 77.199, "end": 77.559}, {"text": " ", "start": 77.559, "end": 77.619}, {"text": "posypeme", "start": 77.619, "end": 78.199}, {"text": " ", "start": 78.199, "end": 78.36}, {"text": "<PERSON><PERSON><PERSON>,", "start": 78.36, "end": 78.939}, {"text": " ", "start": 78.939, "end": 79.019}, {"text": "korením", "start": 79.019, "end": 79.5}, {"text": " ", "start": 79.5, "end": 79.54}, {"text": "a", "start": 79.54, "end": 79.599}, {"text": " ", "start": 79.599, "end": 79.659}, {"text": "trochou", "start": 79.659, "end": 79.979}, {"text": " ", "start": 79.979, "end": 80.04}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>.", "start": 80.04, "end": 80.819}, {"text": "\n", "start": 80.819, "end": 81.08}, {"text": "\n", "start": 81.08, "end": 81.18}, {"text": "<PERSON>", "start": 81.18, "end": 81.479}, {"text": " ", "start": 81.479, "end": 81.5}, {"text": "je", "start": 81.5, "end": 81.539}, {"text": " ", "start": 81.539, "end": 81.619}, {"text": "tu!", "start": 81.619, "end": 82.479}, {"text": " ", "start": 82.479, "end": 82.58}, {"text": "<PERSON>", "start": 82.58, "end": 82.899}, {"text": " ", "start": 82.899, "end": 82.939}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 82.939, "end": 83.54}, {"text": " ", "start": 83.54, "end": 83.599}, {"text": "a", "start": 83.599, "end": 83.659}, {"text": " ", "start": 83.659, "end": 83.699}, {"text": "hovorí:", "start": 83.699, "end": 84.059}, {"text": " ", "start": 84.059, "end": 84.359}, {"text": "„To", "start": 84.359, "end": 84.459}, {"text": " ", "start": 84.459, "end": 84.5}, {"text": "vyzerá", "start": 84.5, "end": 84.86}, {"text": " ", "start": 84.86, "end": 84.939}, {"text": "skvele!", "start": 84.939, "end": 85.459}, {"text": " ", "start": 85.459, "end": 85.739}, {"text": "Č<PERSON>", "start": 85.739, "end": 85.859}, {"text": " ", "start": 85.859, "end": 85.919}, {"text": "to", "start": 85.919, "end": 86.019}, {"text": " ", "start": 86.019, "end": 86.08}, {"text": "je?”", "start": 86.08, "end": 86.959}, {"text": "\n", "start": 86.959, "end": 86.979}, {"text": "\n", "start": 86.979, "end": 87.0}, {"text": "„To", "start": 87.0, "end": 87.079}, {"text": " ", "start": 87.079, "end": 87.119}, {"text": "je", "start": 87.119, "end": 87.219}, {"text": " ", "start": 87.219, "end": 87.36}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 87.36, "end": 87.899}, {"text": " ", "start": 87.899, "end": 87.939}, {"text": "poli<PERSON><PERSON>,", "start": 87.939, "end": 88.459}, {"text": " ", "start": 88.459, "end": 88.739}, {"text": "povedala", "start": 88.739, "end": 89.199}, {"text": " ", "start": 89.199, "end": 89.279}, {"text": "som.", "start": 89.279, "end": 89.459}, {"text": "\n", "start": 89.459, "end": 90.199}, {"text": "\n", "start": 90.199, "end": 90.239}, {"text": "Po", "start": 90.239, "end": 90.319}, {"text": " ", "start": 90.319, "end": 90.4}, {"text": "jedle", "start": 90.4, "end": 90.579}, {"text": " ", "start": 90.579, "end": 90.639}, {"text": "je", "start": 90.639, "end": 90.739}, {"text": " ", "start": 90.739, "end": 90.819}, {"text": "<PERSON>", "start": 90.819, "end": 91.079}, {"text": " ", "start": 91.079, "end": 91.119}, {"text": "<PERSON><PERSON><PERSON>", "start": 91.119, "end": 91.4}, {"text": " ", "start": 91.4, "end": 91.5}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 91.5, "end": 92.039}, {"text": " ", "start": 92.039, "end": 92.099}, {"text": "a", "start": 92.099, "end": 92.159}, {"text": " ", "start": 92.159, "end": 92.199}, {"text": "hovorí…", "start": 92.199, "end": 93.099}, {"text": " ", "start": 93.099, "end": 93.139}, {"text": "„Mmm.", "start": 93.139, "end": 93.979}, {"text": " ", "start": 93.979, "end": 94.019}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 94.019, "end": 94.319}, {"text": " ", "start": 94.319, "end": 94.36}, {"text": "mi", "start": 94.36, "end": 94.439}, {"text": " ", "start": 94.439, "end": 94.479}, {"text": "da<PERSON>", "start": 94.479, "end": 94.659}, {"text": " ", "start": 94.659, "end": 94.699}, {"text": "tento", "start": 94.699, "end": 94.959}, {"text": " ", "start": 94.959, "end": 95.0}, {"text": "recept!", "start": 95.0, "end": 95.539}, {"text": " ", "start": 95.539, "end": 95.799}, {"text": "Je", "start": 95.799, "end": 95.879}, {"text": " ", "start": 95.879, "end": 95.919}, {"text": "to", "start": 95.919, "end": 96.019}, {"text": " ", "start": 96.019, "end": 96.059}, {"text": "<PERSON><PERSON><PERSON>", "start": 96.059, "end": 96.419}, {"text": " ", "start": 96.419, "end": 96.459}, {"text": "tá", "start": 96.459, "end": 96.539}, {"text": " ", "start": 96.539, "end": 96.599}, {"text": "najlepšia", "start": 96.599, "end": 97.139}, {"text": " ", "start": 97.139, "end": 97.18}, {"text": "jar<PERSON>", "start": 97.18, "end": 97.439}, {"text": " ", "start": 97.439, "end": 97.54}, {"text": "polievka!”", "start": 97.54, "end": 98.72}]}