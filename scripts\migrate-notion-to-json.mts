#!/usr/bin/env node
import * as dotenv from 'dotenv';
import { Client } from '@notionhq/client';
import { RoadmapItem } from '../app/roadmap/RoadmapItemCard';
import { PageObjectResponse, QueryDatabaseResponse } from '@notionhq/client/build/src/api-endpoints';
import fs from 'fs';
import path from 'path';

dotenv.config({ path: '.env.development.local' });

const notion = new Client({
  auth: process.env.NOTION_API_KEY || '',
});

async function migrateRoadmapItems(): Promise<void> {
  try {
    console.log('Starting migration from Notion to JSON...');
    
    const databaseId = process.env.NOTION_DATABASE_ID;
    if (!databaseId) {
      throw new Error('NOTION_DATABASE_ID environment variable is not set');
    }
    
    console.log('Fetching roadmap items from Notion...');
    const response: QueryDatabaseResponse = await notion.databases.query({
      database_id: databaseId,
      filter_properties: ['title', '%3BQh%5D', 'vJhL', 'ow~K'],
    //filter_properties: ['name', 'status', 'date', 'description'],
    });
    
    console.log(`Found ${response.results.length} items in Notion database`);
    
    const items: RoadmapItem[] = response.results
      .filter((page): page is PageObjectResponse => page.object === 'page')
      .map((page) => {
        const properties = page.properties;

        const nameProperty = properties.Name as { title: { plain_text: string }[] };
        const statusProperty = properties.Status as { status: { name: string } | null };
        const dateProperty = properties.Date as { date: { start: string } | null };
        const descriptionProperty = properties.Description as { rich_text: { plain_text: string }[] };

        const title = nameProperty?.title?.[0]?.plain_text;

        if (!title) {
          console.warn(`Page ${page.id} is missing a title. Skipping.`);
          return null;
        }

        return {
          id: page.id,
          title: title,
          status: statusProperty?.status?.name ?? undefined,
          date: dateProperty?.date?.start ?? undefined,
          description: descriptionProperty?.rich_text?.[0]?.plain_text ?? undefined,
        };
      })
      .filter((item): item is RoadmapItem => item !== null);
    
    console.log(`Saving ${items.length} items to JSON file...`);
    
    try {
      // Save to a JSON file
      const dataDir = path.join(process.cwd(), 'app', 'roadmap');
      
      // Create the directory if it doesn't exist
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }
      
      const filePath = path.join(dataDir, 'roadmap-items.json');
      fs.writeFileSync(filePath, JSON.stringify(items, null, 2));
      
      console.log(`✅ Successfully saved ${items.length} items to ${filePath}`);
      process.exit(0);
    } catch (error) {
      console.error('❌ Failed to save items to JSON file:', error);
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Migration error:', error);
    process.exit(1);
  }
}

migrateRoadmapItems();
