'use client';

import { useState, useEffect, useRef, useMemo } from 'react';
import {
  PlayIcon,
  PauseIcon,
  Volume2Icon,
  VolumeOffIcon,
  Loader2Icon,
  HighlighterIcon,
  RotateCcwIcon,
  RotateCwIcon,
  RepeatIcon,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { LayoutWrapper } from '@/components/LayoutWrapper';
import { TextPanel } from '@/components/TextPanel';
import { MobileControls } from '@/components/MobileControls';
import { DesktopFloatingControls } from '@/components/DesktopFloatingControls';
import { Dictionary, DictionaryButton } from '@/components/Dictionary';
import { useDevice } from '@/app/providers/DeviceProvider';
import { useAudioTextSync } from '@/lib/hooks/useAudioTextSync';
import { cn, stripPunctuation } from '@/lib/utils/strings';
import { addEnglishTextMarkers } from '@/lib/utils/text-panel';
import { Word } from '@/lib/audio/AudioTextSyncManager';
import { Story } from '@/lib/mocks/stories/stories';

interface StoryClientProps {
  story: Story;
}

export default function StoryClient({ story }: StoryClientProps) {
  const device = useDevice();
  const isDesktop = device.isDesktop;

  const [syncScrollEnabled, setSyncScrollEnabled] = useState(true);
  const [fontSize, setFontSize] = useState(16);
  const [highlightingEnabled, setHighlightingEnabled] = useState(true);
  const [showTranslation, setShowTranslation] = useState(isDesktop);
  const [showFloatingControls, setShowFloatingControls] = useState(false);
  const [showDictionary, setShowDictionary] = useState(false);

  const slovakPanelRef = useRef<HTMLDivElement>(null);
  const englishPanelRef = useRef<HTMLDivElement>(null);
  const combinedPanelRef = useRef<HTMLDivElement>(null);
  const mainControlsRef = useRef<HTMLDivElement>(null);

  const sourceText = story.text || '';
  const translatedText = story.translation || '';
  const audioUrl = story.audioUrl || '';
  const words = story.words || [];

  const AVAILABLE_RATES = [0.75, 0.85, 1, 1.25, 1.5];
  const REWIND_TIME = 5;

  const {
    isPlaying,
    isMuted,
    getCurrentWordIndex,
    currentTime,
    duration,
    volume,
    loop,
    toggle,
    seek,
    setVolume,
    setMute,
    playbackRate,
    setPlaybackRate,
    setLoop,
    isReady,
    play,
  } = useAudioTextSync({
    audioUrl,
    words,
    metadata: {
      title: story.title,
      image: story.imageUrl,
    },
  });

  const sliderValue = isReady ? (currentTime / duration) * 100 : 0;
  const displayTime = (sliderValue / 100) * duration;

  const combinedText = useMemo(() => {
    if (isDesktop) return '';

    const slovakParagraphs = sourceText.split('\n\n');
    const englishParagraphs = translatedText.split('\n\n');
    let combined = '';
    for (
      let i = 0;
      i < Math.max(slovakParagraphs.length, englishParagraphs.length);
      i++
    ) {
      if (slovakParagraphs[i]) {
        combined += slovakParagraphs[i];
        if (englishParagraphs[i]) {
          combined += '\n\n';
        }
      }
      if (showTranslation && englishParagraphs[i]) {
        combined += addEnglishTextMarkers(englishParagraphs[i]);
      }
      if (i < Math.max(slovakParagraphs.length, englishParagraphs.length) - 1) {
        combined += '\n\n';
      }
    }
    return combined;
  }, [sourceText, translatedText, isDesktop, showTranslation]);

  useEffect(() => {
    if (!isDesktop) return;

    const handleScroll = () => {
      if (!mainControlsRef.current) return;

      const mainControlsBottom =
        Number(mainControlsRef.current.getBoundingClientRect().bottom) - 46;
      setShowFloatingControls(mainControlsBottom < 0);
    };

    handleScroll();

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isDesktop]);

  useEffect(() => {
    if (
      !slovakPanelRef.current ||
      !englishPanelRef.current ||
      !syncScrollEnabled ||
      !isDesktop
    )
      return;

    const slovakScrollAreaViewport = slovakPanelRef.current.querySelector(
      '[data-slot="scroll-area-viewport"]',
    ) as HTMLDivElement;
    const englishScrollAreaViewport = englishPanelRef.current.querySelector(
      '[data-slot="scroll-area-viewport"]',
    ) as HTMLDivElement;

    if (!slovakScrollAreaViewport || !englishScrollAreaViewport) return;

    const timeoutId: NodeJS.Timeout | null = null;

    const handleSlovakScroll = () => {
      if (timeoutId) clearTimeout(timeoutId);
      englishScrollAreaViewport.scrollTop = slovakScrollAreaViewport.scrollTop;
    };

    const handleEnglishScroll = () => {
      if (timeoutId) clearTimeout(timeoutId);
      slovakScrollAreaViewport.scrollTop = englishScrollAreaViewport.scrollTop;
    };

    slovakScrollAreaViewport.addEventListener('scroll', handleSlovakScroll);
    englishScrollAreaViewport.addEventListener('scroll', handleEnglishScroll);

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
      if (slovakScrollAreaViewport) {
        slovakScrollAreaViewport.removeEventListener('scroll', handleSlovakScroll);
      }
      if (englishScrollAreaViewport) {
        englishScrollAreaViewport.removeEventListener('scroll', handleEnglishScroll);
      }
    };
  }, [syncScrollEnabled, isDesktop]);

  const formatTime = (time: number) => {
    if (isNaN(time) || time < 0) return '0:00';
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
  };

  const handleSliderValueChange = (value: number[]) => {
    const newTime = (value[0] / 100) * duration;
    seek(newTime);
  };

  const handleVolumeChange = (value: number[]) => {
    setVolume(value[0] / 100);
  };

  const handleRateChange = (rateString: string) => {
    if (!isReady) return;
    const newRate = parseFloat(rateString);
    if (!isNaN(newRate)) {
      setPlaybackRate(newRate);
    }
  };

  const handleRewind = () => {
    if (!isReady || isNaN(duration)) return;
    const newTime = Math.max(0, currentTime - REWIND_TIME);
    seek(newTime);
  };

  const handleForward = () => {
    if (!isReady || isNaN(duration)) return;
    const newTime = Math.min(duration, currentTime + REWIND_TIME);
    seek(newTime);
  };

  const handleWordClick = (word: Word) => {
    if (!isDesktop) return;
    seek(word.start);
    play();
  };

  const handleToggleMute = () => {
    setMute(!isMuted);
  };

  return (
    <LayoutWrapper removePadding={!isDesktop}>
      <main className="relative">
        {isDesktop ? (
          <>
            <div
              ref={mainControlsRef}
              className="flex justify-center gap-3 md:gap-4 mb-4 flex-wrap items-center"
            >
              <Button
                onClick={handleRewind}
                variant="ghost"
                className="h-10 w-10 p-0 relative group"
                disabled={!audioUrl || !isReady}
                aria-label={`Rewind ${REWIND_TIME} seconds`}
              >
                <RotateCcwIcon size={28} />
                <span className="absolute inset-0 flex items-center justify-center text-[10px] font-bold pointer-events-none leading-none">
                  {REWIND_TIME}
                </span>
              </Button>

              <Button
                onClick={toggle}
                variant="default"
                size="default"
                className="flex items-center gap-2 w-[140px]"
                disabled={!audioUrl || !isReady}
              >
                {!isReady && audioUrl ? (
                  <>
                    <Loader2Icon className="h-4 w-4 animate-spin" />
                    <span>Loading...</span>
                  </>
                ) : (
                  <>
                    {isPlaying ? <PauseIcon size={18} /> : <PlayIcon size={18} />}
                    <span>{isPlaying ? 'Pause' : 'Play'}</span>
                  </>
                )}
              </Button>

              <Button
                onClick={handleForward}
                variant="ghost"
                className="h-10 w-10 p-0 relative group"
                disabled={!audioUrl || !isReady}
                aria-label={`Forward ${REWIND_TIME} seconds`}
              >
                <RotateCwIcon size={28} />
                <span className="absolute inset-0 flex items-center justify-center text-[10px] font-bold text-foreground pointer-events-none leading-none">
                  {REWIND_TIME}
                </span>
              </Button>

              {audioUrl && (
                <div className="text-sm font-mono min-w-[90px] text-center">
                  {formatTime(displayTime)} / {formatTime(duration)}
                </div>
              )}

              <div className="flex items-center gap-2">
                <Button variant="ghost" size="icon" onClick={handleToggleMute}>
                  {isMuted ? <VolumeOffIcon size={16} /> : <Volume2Icon size={16} />}
                </Button>
                <Slider
                  value={[volume * 100]}
                  onValueChange={handleVolumeChange}
                  min={0}
                  max={100}
                  step={1}
                  className="w-24"
                  aria-label="Audio volume"
                  disabled={!audioUrl}
                />
              </div>
            </div>

            <div className="mb-8 max-w-2xl mx-auto px-4">
              <Slider
                value={[sliderValue]}
                defaultValue={[sliderValue]}
                min={0}
                max={100}
                step={0.1}
                onValueChange={handleSliderValueChange}
                className="w-full"
                aria-label="Audio playback position"
                disabled={!isReady}
              />
            </div>

            <div className="flex justify-center gap-2 md:gap-4 mb-8 flex-wrap items-center">
              {showTranslation && (
                <Button
                  onClick={() => setSyncScrollEnabled(!syncScrollEnabled)}
                  variant={syncScrollEnabled ? 'secondary' : 'outline'}
                  size="sm"
                >
                  Sync Scroll: {syncScrollEnabled ? 'ON' : 'OFF'}
                </Button>
              )}

              <Button
                onClick={() => setHighlightingEnabled(!highlightingEnabled)}
                variant={highlightingEnabled ? 'secondary' : 'outline'}
                size="sm"
                className="flex items-center gap-1"
              >
                <HighlighterIcon size={16} />
                Highlight: {highlightingEnabled ? 'ON' : 'OFF'}
              </Button>

              <Button
                onClick={() => {
                  setLoop(!loop);
                }}
                variant={loop ? 'secondary' : 'outline'}
                size="sm"
                className="flex items-center gap-1"
              >
                <RepeatIcon size={16} />
                Loop: {loop ? 'ON' : 'OFF'}
              </Button>

              <Button
                onClick={() => setShowTranslation(!showTranslation)}
                variant={showTranslation ? 'secondary' : 'outline'}
                size="sm"
                className="flex items-center gap-1"
              >
                Translation: {showTranslation ? 'ON' : 'OFF'}
              </Button>

              {story.dictionary && story.dictionary.length > 0 && (
                <DictionaryButton
                  onClick={() => setShowDictionary(!showDictionary)}
                  isActive={showDictionary}
                />
              )}

              <div className="flex items-center gap-1">
                <Button
                  onClick={() => setFontSize((prev) => Math.max(10, prev - 1))}
                  variant="outline"
                  size="icon"
                >
                  <span className="text-xs">A-</span>
                </Button>
                <Button
                  onClick={() => setFontSize((prev) => prev + 1)}
                  variant="outline"
                  size="icon"
                >
                  <span className="text-base">A+</span>
                </Button>
              </div>

              <div className="flex items-center gap-2">
                <Select
                  value={playbackRate.toString()}
                  onValueChange={handleRateChange}
                  disabled={!audioUrl || !isReady}
                >
                  <SelectTrigger className="w-[145px] h-9">
                    <span>Speed:</span>
                    <span className="mr-auto">
                      <SelectValue placeholder="Speed" />
                    </span>
                  </SelectTrigger>
                  <SelectContent>
                    {AVAILABLE_RATES.map((rate) => (
                      <SelectItem key={rate} value={rate.toString()}>
                        {rate}x
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </>
        ) : null}

        <div
          className={cn(
            'grid gap-8',
            showTranslation && isDesktop ? 'md:grid-cols-2' : 'max-w-[625px] mx-auto',
            !isDesktop && 'mt-2',
          )}
        >
          <div className={!isDesktop ? 'hidden' : ''}>
            <TextPanel
              title={story.title}
              panelRef={slovakPanelRef}
              content={sourceText}
              words={words}
              currentWordIndex={getCurrentWordIndex()}
              fontSize={fontSize}
              highlightingEnabled={highlightingEnabled}
              onWordClick={handleWordClick}
              dictionary={story.dictionary}
            />
          </div>

          {showTranslation && isDesktop && (
            <TextPanel
              title={story.englishTitle}
              panelRef={englishPanelRef}
              content={translatedText}
              words={[]}
              currentWordIndex={-1}
              fontSize={fontSize}
            />
          )}

          <div className={isDesktop ? 'hidden' : ''}>
            <TextPanel
              title={story.title}
              panelRef={combinedPanelRef}
              content={combinedText}
              words={words}
              currentWordIndex={getCurrentWordIndex()}
              fontSize={fontSize}
              highlightingEnabled={highlightingEnabled}
              onWordClick={handleWordClick}
              dictionary={story.dictionary}
            />
          </div>
        </div>
        {isDesktop && story.dictionary && story.dictionary.length > 0 && (
          <Dictionary
            entries={story.dictionary}
            open={showDictionary}
            onOpenChange={setShowDictionary}
            onWordClick={(word) => {
              if (!slovakPanelRef.current) return;

              const wordObj = words.find(
                (_word) =>
                  stripPunctuation(_word.text.toLowerCase()) ===
                  stripPunctuation(word.toLowerCase()),
              );

              if (!wordObj) return;

              seek(wordObj.start);

              document.querySelectorAll('.word-flash').forEach((el) => {
                el.classList.remove('word-flash');
              });

              setTimeout(() => {
                if (!slovakPanelRef.current) return;
                const wordElements =
                  slovakPanelRef.current.querySelectorAll<HTMLSpanElement>(
                    'span[data-word-index]',
                  );

                let firstElement: HTMLSpanElement | undefined;

                wordElements.forEach((element) => {
                  const index = parseInt(element.getAttribute('data-word-index') || '-1');
                  if (
                    index >= 0 &&
                    words[index] &&
                    stripPunctuation(words[index].text.toLowerCase()) ===
                      stripPunctuation(word.toLowerCase())
                  ) {
                    element.classList.add('word-flash');
                    if (!firstElement) firstElement = element;
                  }
                });

                if (firstElement) {
                  firstElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
              }, 10);
            }}
          />
        )}

        {!isDesktop && (
          <MobileControls
            isPlaying={isPlaying}
            isReady={isReady}
            audioUrl={audioUrl}
            currentTime={currentTime}
            duration={duration}
            volume={volume}
            playbackRate={playbackRate}
            toggle={toggle}
            seek={seek}
            setVolume={setVolume}
            setPlaybackRate={setPlaybackRate}
            handleRewind={handleRewind}
            handleForward={handleForward}
            fontSize={fontSize}
            setFontSize={setFontSize}
            highlightingEnabled={highlightingEnabled}
            setHighlightingEnabled={setHighlightingEnabled}
            showTranslation={showTranslation}
            isLooping={loop}
            setShowTranslation={setShowTranslation}
            setIsLooping={setLoop}
            AVAILABLE_RATES={AVAILABLE_RATES}
            REWIND_TIME={REWIND_TIME}
          />
        )}

        {isDesktop && (
          <div
            className={`transition-opacity duration-300 ${showFloatingControls ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}
          >
            <DesktopFloatingControls
              isPlaying={isPlaying}
              isReady={isReady}
              audioUrl={audioUrl}
              currentTime={currentTime}
              duration={duration}
              volume={volume}
              playbackRate={playbackRate}
              loop={loop}
              toggle={toggle}
              seek={seek}
              setVolume={setVolume}
              setPlaybackRate={setPlaybackRate}
              setLoop={setLoop}
              handleRewind={handleRewind}
              handleForward={handleForward}
              REWIND_TIME={REWIND_TIME}
              AVAILABLE_RATES={AVAILABLE_RATES}
            />
          </div>
        )}
        {isDesktop && <div className="h-16"></div>}
      </main>
    </LayoutWrapper>
  );
}
