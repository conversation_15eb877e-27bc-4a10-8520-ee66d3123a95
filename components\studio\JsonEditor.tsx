'use client';

import { useState } from 'react';
import { toast } from 'sonner';
import { SaveIcon, UploadIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import type { WordAlignment } from '@/lib/internal-api/types';
import type { Story } from '@/lib/mocks/stories/stories';
import { EditableTextPanel } from './EditableTextPanel';

export interface JsonEditorProps {
  onImport?: (story: Story) => void;
}

export default function JsonEditor({ onImport }: JsonEditorProps) {
  const [importedStory, setImportedStory] = useState<Story | null>(null);
  const [editedWords, setEditedWords] = useState<WordAlignment[]>([]);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');

  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        const storyData = JSON.parse(content) as Story;

        if (!storyData.words || !Array.isArray(storyData.words)) {
          toast.error('Invalid JSON format: missing words array');
          return;
        }

        setImportedStory(storyData);
        setEditedWords(storyData.words);
        setTitle(storyData.title || '');
        setDescription(storyData.description || '');

        if (onImport) {
          onImport(storyData);
        }

        toast.success(`Successfully imported "${storyData.title}"`);
      } catch (error) {
        console.error('Error parsing JSON file:', error);
        toast.error('Failed to parse JSON file. Please check the file format.');
      }
    };

    reader.readAsText(file);
  };

  const handleUpdateWord = (index: number, updatedWord: WordAlignment) => {
    const newWords = [...editedWords];
    newWords[index] = updatedWord;
    setEditedWords(newWords);
  };

  const handleSaveEditedStory = () => {
    if (!importedStory || !editedWords.length) {
      toast.error('No story data to save');
      return;
    }

    const updatedStory: Story = {
      ...importedStory,
      words: editedWords,
      title: title || importedStory.title,
      description: description || importedStory.description,
    };

    const jsonString = JSON.stringify(updatedStory, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;

    const filename = `${updatedStory.title.trim().toLowerCase().replace(/\s+/g, '-') || 'story'}.json`;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    toast.success(`Story JSON saved as ${filename}`);
  };

  return (
    <div className="space-y-6">
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Import JSON Story</CardTitle>
          <CardDescription>
            Import a JSON file with words and audio URL to edit timestamps.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4">
            <div>
              <Label htmlFor="jsonFile">Select JSON File</Label>
              <div className="flex gap-2 mt-1">
                <Input
                  id="jsonFile"
                  type="file"
                  accept=".json"
                  onChange={handleFileImport}
                  className="flex-1"
                />
                <Button
                  variant="secondary"
                  className="gap-1"
                  onClick={() => document.getElementById('jsonFile')?.click()}
                >
                  <UploadIcon className="h-4 w-4" />
                  Import
                </Button>
              </div>
            </div>

            {importedStory && (
              <div className="grid gap-4">
                <div>
                  <Label htmlFor="storyTitle">Title</Label>
                  <Input
                    id="storyTitle"
                    value={title}
                    onChange={(event) => setTitle(event.target.value)}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="storyDescription">Description</Label>
                  <Input
                    id="storyDescription"
                    value={description}
                    onChange={(event) => setDescription(event.target.value)}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="audio_url">Audio URL</Label>
                  <Input
                    id="audio_url"
                    value={importedStory.audioUrl || ''}
                    readOnly
                    className="mt-1 font-mono text-xs"
                  />
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {importedStory && (
        <div className="space-y-6">
          <Card>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-center">
                <CardTitle>Edit Word Timestamps</CardTitle>
                <Button onClick={handleSaveEditedStory} size="sm" className="gap-1">
                  <SaveIcon className="h-4 w-4" />
                  Save Changes
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <EditableTextPanel
                title=""
                content={importedStory.text}
                words={editedWords}
                fontSize={16}
                onWordUpdate={handleUpdateWord}
                audioUrl={importedStory.audioUrl}
              />
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
