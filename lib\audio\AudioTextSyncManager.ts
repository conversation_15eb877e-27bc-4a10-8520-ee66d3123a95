import { WordAlignment } from '@/lib/internal-api/types';

export interface Word extends WordAlignment {
  index?: number;
  isContentWord?: boolean;
}

type PlayStateChangeCallback = (isPlaying: boolean) => void;
type TimeUpdateCallback = (time: number) => void;
type LoadedDataCallback = (duration: number) => void;

export class AudioTextSyncManager {
  private audioElement: HTMLAudioElement;
  private words: Word[];
  private _loop: boolean = false;

  public onPlayStateChange: PlayStateChangeCallback | null = null;
  public onTimeUpdate: TimeUpdateCallback | null = null;
  public onLoadedData: LoadedDataCallback | null = null;
  public onEnded: (() => void) | null = null;

  constructor(audioElement: HTMLAudioElement, words: Word[]) {
    this.audioElement = audioElement;
    this.audioElement.preload = 'auto';
    this.audioElement.loop = this._loop;

    this.words = words.map((word, index) => {
      const isContentWord = word.text.trim().length > 0;

      // Special handling for words with escaped quotes because otherwise will get ignored
      const hasEscapedQuotes = word.text.includes('\\"');

      return {
        ...word,
        index,
        isContentWord: isContentWord || hasEscapedQuotes,
      };
    });

    this.attachListeners();
  }

  private attachListeners(): void {
    this.audioElement.addEventListener('play', this.handlePlay);
    this.audioElement.addEventListener('pause', this.handlePause);
    this.audioElement.addEventListener('ended', this.handleEnded);
    this.audioElement.addEventListener('timeupdate', this.handleTimeUpdate);
    this.audioElement.addEventListener('loadedmetadata', this.handleLoadedMetadata);
  }

  private removeListeners(): void {
    this.audioElement.removeEventListener('play', this.handlePlay);
    this.audioElement.removeEventListener('pause', this.handlePause);
    this.audioElement.removeEventListener('ended', this.handleEnded);
    this.audioElement.removeEventListener('timeupdate', this.handleTimeUpdate);
    this.audioElement.removeEventListener('loadedmetadata', this.handleLoadedMetadata);
  }

  private handlePlay = () => {
    this.onPlayStateChange?.(true);
    if ('mediaSession' in navigator) {
      navigator.mediaSession.playbackState = 'playing';
    }
  };

  private handlePause = () => {
    this.onPlayStateChange?.(false);
    if ('mediaSession' in navigator) {
      navigator.mediaSession.playbackState = 'paused';
    }
  };

  private handleEnded = () => {
    if (this._loop) {
      // If looping is enabled, restart playback from the beginning
      this.seek(0);
      this.play();
    } else {
      this.onPlayStateChange?.(false);
      this.onTimeUpdate?.(this.audioElement.currentTime);
      this.onEnded?.();
    }
  };

  private lastTimeUpdateTime: number = 0;
  private readonly TIME_UPDATE_THROTTLE_MS: number = 16; // ~60fps

  private handleTimeUpdate = () => {
    const now = performance.now();
    // Throttle timeupdate events to avoid excessive updates
    // but ensure we're still responsive enough for smooth highlighting
    if (now - this.lastTimeUpdateTime >= this.TIME_UPDATE_THROTTLE_MS) {
      this.lastTimeUpdateTime = now;
      this.onTimeUpdate?.(this.audioElement.currentTime);
      this.handleUpdatePositionState();
    }
  };

  private handleUpdatePositionState = () => {
    if ('mediaSession' in navigator && this.audioElement.currentTime !== undefined) {
      navigator.mediaSession.setPositionState({
        duration: this.audioElement.duration,
        playbackRate: this.audioElement.playbackRate,
        position: this.audioElement.currentTime,
      });
    }
  };

  private handleLoadedMetadata = () => {
    if (!isNaN(this.audioElement.duration)) {
      this.onLoadedData?.(this.audioElement.duration);
    }
  };

  public get internalWords(): Word[] {
    return this.words;
  }

  public get rawAudioElement(): HTMLAudioElement {
    return this.audioElement;
  }

  public get isPlaying(): boolean {
    return !this.audioElement.paused;
  }

  public get currentTime(): number {
    return this.audioElement.currentTime;
  }

  public get duration(): number {
    return this.audioElement.duration;
  }

  public get isReady(): boolean {
    return this.audioElement.readyState >= 2 && !isNaN(this.audioElement.duration);
  }

  public play(): void {
    if (this.audioElement.paused) {
      this.audioElement.play().catch((err) => console.error('Audio play failed:', err));
    }
  }

  public pause(): void {
    if (!this.audioElement.paused) {
      this.audioElement.pause();
    }
  }

  public toggle(): void {
    if (this.audioElement.paused) {
      this.play();
    } else {
      this.pause();
    }
  }

  public seek(time: number): void {
    const duration = this.audioElement.duration;
    if (!isNaN(duration)) {
      const newTime = Math.max(0, Math.min(time, duration));
      this.audioElement.currentTime = newTime;
      this.onTimeUpdate?.(newTime);
    }
  }

  public setVolume(level: number): void {
    const newVolume = Math.max(0, Math.min(level, 1));
    this.audioElement.volume = newVolume;
  }

  public setPlaybackRate(rate: number): void {
    this.audioElement.playbackRate = rate;
  }

  public get loop(): boolean {
    return this._loop;
  }

  public setLoop(loop: boolean): void {
    this._loop = loop;
    this.audioElement.loop = loop;
  }

  public setMute(mute: boolean): void {
    this.audioElement.muted = mute;
  }

  public destroy(): void {
    this.removeListeners();
    if (this.audioElement) {
      this.audioElement.pause();
      this.audioElement.src = '';
    }
    this.onPlayStateChange = null;
    this.onTimeUpdate = null;
    this.onLoadedData = null;
    this.onEnded = null;
  }
}
