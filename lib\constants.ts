import { stories } from './mocks/stories/stories';

export const BASE_URL =
  process.env.NODE_ENV === 'production'
    ? 'https://beta.osemjeden.com'
    : 'http://localhost:3000';
export const SWITCH_ON_AUDIO_URL =
  'https://x9xfaca7mb.ufs.sh/f/TuGkI0HClmz3PojowDHBxAbEGviSjLzo974N3B1VrpWqZ2m0';
export const SWITCH_OFF_AUDIO_URL =
  'https://x9xfaca7mb.ufs.sh/f/TuGkI0HClmz3fLYq0YQNtLdsGDPhzXaRnT6BY4f3c0vbZ1Eg';
export const PAGE_CLASSNAME = 'pt-24 px-4 md:px-8';
export const VOICES = {
  //KATARINA: '3xGjYLloEIIWjYI5Bee0',
  KATARINA: {
    id: 'Y7LuuXmOcq2mxQM3UPYq',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    preview: '',
  },
  PAULINA: {
    id: 'Rpg8Sn3cVL1f8658yYm2',
    name: '<PERSON><PERSON>a - <PERSON>',
    preview: '',
  },
  TOMAŠ: {
    id: 'gs3k6LpQrYIz2zSIf0r2',
    name: 'Tomaš',
    preview: '',
  },
  HOPE: {
    id: 'OYTbf65OHHFELVut7v2H',
    name: 'Hope',
    preview: '',
  },
  EMMA: {
    id: 'pPdl9cQBQq4p6mRkZy2Z',
    name: 'Emma',
    preview: '',
  },
  JEANETTE: {
    id: 'RILOU7YmBhvwJGDGjNmP',
    name: 'Jeanette',
    preview: '',
  },
  PETER: {
    id: 'd6IbhdqAKkXCCVuJjbie',
    name: 'Peter',
    preview: '',
  },
  ZUZANA: {
    id: 'AZKbYmQbJEONExTR4mbE',
    name: 'Zuzana',
    preview: '',
  },
  MONIKA: {
    id: 'dl4FkEvdYqvECrmvMbnF',
    name: 'Monika',
    preview: '',
  },
  ALICE: {
    id: 'Xb7hH8MSUJpSbSDYk0k2',
    name: 'Alice',
    preview: '',
  },
};

export const VOICES_WITH_PREVIEW = Object.fromEntries(
  Object.entries(VOICES).map(([key, value]) => {
    const voiceFromStory = stories.find((story) => story.voiceId === value.id)?.audioUrl;
    return [key, { ...value, preview: voiceFromStory }];
  }),
);

export const ELEVENLABS_PRONUNCIATION_DICTIONARY_ID = 'U6A98echavtloKy3vfE8';
export const ELEVENLABS_PRONUNCIATION_DICTIONARY_VERSION_ID = 'HPxL0lPFvh00AxDUfoxT';
