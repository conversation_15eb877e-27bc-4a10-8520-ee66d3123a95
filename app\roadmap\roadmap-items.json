[{"id": "1f199482-da46-80bf-9894-ff55675c9f56", "title": "Share link", "status": "Planned", "description": "Button to copy or share link"}, {"id": "1f199482-da46-8086-8ea8-f64cd0ec7bce", "title": "Follow paragraph", "status": "Planned", "description": "The scroll should follow the current paragraph as it is playing"}, {"id": "1f199482-da46-8048-932a-ea56fc914ecc", "title": "Paragraph highlighting", "status": "Planned", "description": "Option to follow highlight paragraphs"}, {"id": "1f099482-da46-8072-a3df-c13104879786", "title": "Clean up studio view", "status": "Planned"}, {"id": "1ec99482-da46-80d4-bef7-c44a13a5b6de", "title": "Howler Integration", "status": "Planned", "description": "Replace Native Web Audio in place of <PERSON><PERSON>"}, {"id": "1e299482-da46-80ba-bde9-d739e2c4a320", "title": "Automatic studio translations", "status": "Planned"}, {"id": "1e199482-da46-80aa-8c8d-d59e2f6fa1cb", "title": "Implement CMS", "status": "Planned"}, {"id": "1e199482-da46-80be-86d1-e8500d20a454", "title": "Investigate weird text copying", "status": "Planned"}, {"id": "1de99482-da46-80b4-bf9f-dfd54bbfc14e", "title": "Installable PWA", "status": "Completed", "description": "Made web app installable via PWA"}, {"id": "1db99482-da46-8097-94b0-dcaa38ecb38b", "title": "Keyboard navigation support", "status": "Planned"}, {"id": "1db99482-da46-8059-b0c3-e1799bf2c9a8", "title": "Improve mobile navigation", "status": "Completed"}, {"id": "1db99482-da46-80ae-bfe7-f7e032b6fa65", "title": "Progress indicator", "status": "Planned", "description": "Shows how much of a story was read"}, {"id": "1db99482-da46-800d-b992-d2da7bcc3eb9", "title": "Implement filters", "status": "Planned"}, {"id": "1db99482-da46-80f7-8f48-d948d1a1ccb1", "title": "Add reading time estimates", "status": "Planned", "description": "Tags, difficulty level, atď."}, {"id": "1db99482-da46-806a-bf5f-f8ac78c8eab9", "title": "Implement search", "status": "Planned", "description": "Search bar to allow users to find stories by title"}, {"id": "1db99482-da46-8090-b1d3-eab80c6c0bc9", "title": "Use WebM over MP3", "status": "Completed", "description": "For performance. duh"}, {"id": "1d499482-da46-8018-8550-e7b7d22679fe", "title": "Text highlighting causes text to jump around when container width is smaller", "status": "Completed"}, {"id": "1d499482-da46-80bd-bbc7-f13c470ebd78", "title": "Seekable words", "status": "Completed", "description": "Allow skipping audio to word on click"}, {"id": "1d299482-da46-8066-9d13-e04baf922a5c", "title": "Change colors of navigation links", "status": "Completed", "description": "Will figure out special Roadmap color later"}, {"id": "1d299482-da46-8012-a6cf-ca8656b875aa", "title": "Back button once you click a story", "status": "Planned"}, {"id": "1d299482-da46-800c-816b-efa247ccf19b", "title": "Add playback rate", "status": "Completed", "description": "Option to change the speed of a story"}, {"id": "1d099482-da46-8079-b064-e0766386872f", "title": "Ability to hide translation text", "status": "Completed"}, {"id": "1cf99482-da46-8024-9e09-eebf5eb3c68b", "title": "NAJLAPŠIE slovenské recepty", "status": "Planned", "description": "Just to share foods from friends"}, {"id": "1cf99482-da46-80be-bb2c-f9ddeb049bba", "title": "AI Slovak Chatbot", "status": "Planned", "description": "Literally just a chatbot"}, {"id": "1cf99482-da46-80eb-a136-c0fbb45f014c", "title": "Persistent user preferences", "status": "Planned", "description": "Font size, highlighting on/off, volume"}, {"id": "1cf99482-da46-8006-a44c-e0f614ade7e1", "title": "Mobile responsiveness", "status": "Completed"}, {"id": "1cf99482-da46-8029-93d4-c99e345b2e7b", "title": "Add story titles", "status": "Completed", "description": "Either strip the first “sentence” and use as title or use title field from data source"}, {"id": "1ce99482-da46-8072-99e2-e386ea7a4b32", "title": "Dark mode text readibility", "status": "Planned", "description": "Dark mode has a visual glitch. This will be fun to fix…"}, {"id": "1ce99482-da46-8064-9063-f141bf8c7c56", "title": "Offline support", "status": "Planned", "description": "because what if my internet goes off… 🤯 or yours"}, {"id": "1ce99482-da46-803e-ac83-e0fbe245a6ca", "title": "User dictionaries", "status": "Planned", "description": "Place to store learned or difficult words and translations"}, {"id": "1ce99482-da46-804f-b494-f7d88ba682ad", "title": "Content library", "status": "Planned", "description": "Place to view all stories"}, {"id": "1ce99482-da46-802f-ab67-f130a1f2ac0a", "title": "Translate words on click", "status": "Planned", "description": "I click word, you give translation"}, {"id": "1ce99482-da46-801d-86d7-ceda9b24adf5", "title": "Ability to import texts", "status": "Planned"}, {"id": "1ce99482-da46-80ee-911d-ff660e297180", "title": "Light/Dark Mode", "status": "Completed", "description": "Pretty obvious what this is"}, {"id": "1ce99482-da46-8159-ac32-ed167733ad9b", "title": "Bimodal listening", "status": "Completed", "date": "2025-04-06", "description": "Follow along as words are highlighted in sync with audio"}, {"id": "1ce99482-da46-81ab-8760-d21934a780ef", "title": "AI Slovak Tutor", "status": "Planned", "date": "2025-04-07", "description": "This is not an AI girlfriend"}]