import { NextRequest, NextResponse } from 'next/server';
import { ElevenLabsClient } from 'elevenlabs';

const client = new ElevenLabsClient({
  apiKey: process.env.ELEVENLABS_API_KEY || '',
});

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const audioFile = formData.get('audio_file') as File;
    const text = formData.get('text') as string;

    if (!audioFile) {
      return NextResponse.json({ error: 'Audio file is required' }, { status: 400 });
    }

    if (!text || !text.trim()) {
      return NextResponse.json({ error: 'Text is required' }, { status: 400 });
    }

    const arrayBuffer = await audioFile.arrayBuffer();
    const blob = new Blob([arrayBuffer], { type: audioFile.type });

    const response = await client.forcedAlignment.create({
      file: blob,
      text: text,
    });

    return NextResponse.json(response);
  } catch (error) {
    console.error('API Error generating word alignment:', error);
    return NextResponse.json(
      { error: 'API Error generating word alignment' },
      { status: 500 },
    );
  }
}
