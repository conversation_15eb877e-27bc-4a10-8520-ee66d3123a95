import { NextRequest, NextResponse } from 'next/server';
import { convertCharacterAlignmentToWordAlignment, WordsAlignment } from './utils';
import { CharactersAlignment } from '@/lib/internal-api/types';

export async function POST(request: NextRequest) {
  try {
    const rawData: { alignment: CharactersAlignment } = await request.json();

    if (!rawData || !rawData.alignment) {
      return NextResponse.json(
        {
          error: 'Invalid input data. Expecting JSON object with an "alignment" key.',
        },
        { status: 400 },
      );
    }

    const convertedData: WordsAlignment =
      convertCharacterAlignmentToWordAlignment(rawData);

    return NextResponse.json(convertedData, { status: 200 });
  } catch (error: unknown) {
    console.error('API Error converting character alignment to word alignment:', error);

    if (error instanceof Error && error.message.startsWith('Invalid input data')) {
      return NextResponse.json(
        { error: `Conversion failed: ${error.message}` },
        { status: 400 },
      );
    }

    if (error instanceof SyntaxError) {
      return NextResponse.json({ error: 'Invalid JSON received.' }, { status: 400 });
    }

    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
