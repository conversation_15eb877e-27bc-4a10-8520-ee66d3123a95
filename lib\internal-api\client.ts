import { z } from 'zod';
import {
  GenerateResponseBody,
  ConvertRequestBody,
  ConvertRequestBodySchema,
  ConvertResponseBody,
  ConvertResponseBodySchema,
  ApiErrorResponse,
  ApiErrorResponseSchema,
  GenerateAudioRequestBodySchema,
  GenerateAudioRequestBody,
  GenerateWordsRequestBody,
  GenerateWordsRequestBodySchema,
  GenerateWordsResponseBody,
  GenerateWordsResponseBodySchema,
  GenerateAudioResponseBodySchema,
} from '@/lib/internal-api/types';

export class ApiError extends Error {
  status: number;
  errorData?: ApiErrorResponse;

  constructor(message: string, status: number, errorData?: ApiErrorResponse) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.errorData = errorData;
    Object.setPrototypeOf(this, ApiError.prototype);
  }
}

export class ApiClient {
  /**
   * Base fetch method with Zod parsing.
   */
  private static async request<RequestBody, ResponseSchema extends z.ZodType>(
    endpoint: string,
    schema: ResponseSchema,
    options: RequestInit = {},
    requestBody?: RequestBody,
  ): Promise<z.infer<ResponseSchema>> {
    const defaultHeaders: HeadersInit = {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    };

    const config: RequestInit = {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    };

    if (requestBody) {
      config.body = JSON.stringify(requestBody);
    }

    let response: Response;
    try {
      response = await fetch(endpoint, config);
    } catch (networkError) {
      console.error(
        `(APIClientError: ${endpoint}) - Network error while fetching ${endpoint}: ${networkError instanceof Error ? networkError.message : 'Unknown network error'}`,
      );
      return null;
    }

    if (!response.ok) {
      let errorMsg = `API request to ${endpoint} failed with status ${response.status}`;
      let errorData: ApiErrorResponse | undefined;
      try {
        const responseBody = await response.json();
        const parsedError = ApiErrorResponseSchema.safeParse(responseBody);
        if (parsedError.success) {
          errorData = parsedError.data;
          errorMsg = `${errorMsg}: ${errorData.error}`;
        } else {
          errorMsg = `${errorMsg}: ${response.statusText} (Error response body missing or malformed)`;
        }
      } catch {
        errorMsg = `${errorMsg}: ${response.statusText} (Could not parse error response)`;
      }
      throw new ApiError(errorMsg, response.status, errorData);
    }

    if (response.status === 204 || response.headers.get('content-length') === '0') {
      // Zod schemas usually expect an object/value. Handle how you want an empty successful response.
      // Returning null or an empty object might be suitable depending on the schema.
      // For this example, let's assume an empty object might be parsable by some schemas (like optional fields)
      // or throw if the schema requires data.
      const parseResult = schema.safeParse({});
      if (parseResult.success) {
        return parseResult.data;
      }

      console.error(
        `(APIClientError: ${endpoint}) - API returned an empty response, but the schema requires data.`,
      );
      return null;
    }

    try {
      const data = await response.json();
      const parseResult = schema.safeParse(data);
      if (!parseResult.success) {
        console.error(
          `(APIClientError: ${endpoint}) - ZOD failed to parse successful response:`,
          parseResult.error.errors,
        );
      }
      return parseResult.data;
    } catch (jsonError) {
      console.error(
        `(APIClientError: ${endpoint}) - Failed to parse JSON response: ${jsonError instanceof Error ? jsonError.message : 'Unknown JSON error'}`,
      );
      return null;
    }
  }

  /**
   * Generate words alignment from text and audio.
   */
  static async generateWordsAlignment(
    data: GenerateWordsRequestBody,
  ): Promise<GenerateWordsResponseBody> {
    GenerateWordsRequestBodySchema.parse(data);
    return this.request<GenerateWordsRequestBody, typeof GenerateWordsResponseBodySchema>(
      '/api/tts/generate-words',
      GenerateWordsResponseBodySchema,
      { method: 'POST' },
      data,
    );
  }

  /**
   * Generate audio from text.
   */
  static async generateAudio(
    data: GenerateAudioRequestBody,
  ): Promise<GenerateResponseBody> {
    GenerateAudioRequestBodySchema.parse(data);
    return this.request<GenerateAudioRequestBody, typeof GenerateAudioResponseBodySchema>(
      '/api/tts/generate-audio',
      GenerateAudioResponseBodySchema,
      { method: 'POST' },
      data,
    );
  }

  /**
   * Convert character alignment to word alignment.
   */
  static async convertCharacterAlignmentToWords(
    data: ConvertRequestBody,
  ): Promise<ConvertResponseBody> {
    ConvertRequestBodySchema.parse(data);
    return this.request<ConvertRequestBody, typeof ConvertResponseBodySchema>(
      '/api/tts/convert-characters-to-words',
      ConvertResponseBodySchema,
      { method: 'POST' },
      data,
    );
  }
}
