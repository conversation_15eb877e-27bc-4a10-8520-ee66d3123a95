'use client';

import React, { memo, RefObject, useRef, useMemo, useState } from 'react';
import { PlayIcon, CheckIcon, XIcon, PauseIcon } from 'lucide-react';
import type { WordAlignment } from '@/lib/internal-api/types';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils/strings';
import { Popover, PopoverContent, PopoverAnchor } from '@/components/ui/popover';
import { useAudioTextSync } from '@/lib/hooks/useAudioTextSync';

interface ExtendedWord extends WordAlignment {
  originalIndex: number;
}

interface EditableTextPanelProps {
  title: string;
  content: string;
  words: WordAlignment[];
  fontSize?: number;
  panelRef?: RefObject<HTMLDivElement | null>;
  textContentRef?: RefObject<HTMLDivElement | null>;
  onWordUpdate: (index: number, updatedWord: WordAlignment) => void;
  audioUrl?: string;
}

export const EditableTextPanel = memo(function EditableTextPanel({
  title,
  content,
  words,
  fontSize = 16,
  panelRef,
  textContentRef,
  onWordUpdate,
  audioUrl = '',
}: EditableTextPanelProps) {
  const scrollViewportRef = useRef<HTMLDivElement | null>(null);
  const [selectedWordIndex, setSelectedWordIndex] = useState<number | null>(null);
  const [editStartTime, setEditStartTime] = useState<string>('');
  const [editEndTime, setEditEndTime] = useState<string>('');
  const [editWordText, setEditWordText] = useState<string>('');
  const [selectedWordElement, setSelectedWordElement] = useState<HTMLElement | null>(
    null,
  );

  const { isPlaying, getCurrentWordIndex, currentTime, duration, toggle, seek, play } =
    useAudioTextSync({
      audioUrl,
      words,
      metadata: {
        title,
        image: '',
      },
    });

  const currentWordIndex = getCurrentWordIndex();

  const formatTime = (seconds: number): string => {
    if (isNaN(seconds)) return '00:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleSelectWord = (
    word: WordAlignment,
    index: number,
    event: React.MouseEvent<HTMLSpanElement>,
  ) => {
    const wordIndex =
      (word as ExtendedWord).originalIndex !== undefined
        ? (word as ExtendedWord).originalIndex
        : index;

    setSelectedWordIndex(wordIndex);
    setEditStartTime(word.start.toFixed(3));
    setEditEndTime(word.end.toFixed(3));
    setEditWordText(word.text);
    setSelectedWordElement(event.currentTarget);
  };

  const handleSaveEdit = () => {
    if (selectedWordIndex === null) return;

    const start = parseFloat(editStartTime);
    const end = parseFloat(editEndTime);

    if (isNaN(start) || isNaN(end)) return;

    onWordUpdate(selectedWordIndex, {
      text: editWordText,
      start,
      end,
    });

    setSelectedWordIndex(null);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (selectedWordIndex === null) return;

    if (e.key === 'Enter') {
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      setSelectedWordIndex(null);
    }
  };

  const handlePlayWord = () => {
    if (selectedWordIndex === null) return;
    const startTime = parseFloat(editStartTime);
    if (!isNaN(startTime)) {
      seek(startTime);
      play();
    }
  };

  const renderedContent = useMemo(() => {
    if (!content || !words || words.length === 0) return null;

    const paragraphs = content.split('\n\n');
    const finalParagraphs: React.ReactNode[] = [];
    let wordSearchIndex = 0;

    paragraphs.forEach((paragraph, pIndex) => {
      if (!paragraph.trim()) return;

      const paragraphNodes: React.ReactNode[] = [];
      let lastParagraphIndex = 0;

      while (wordSearchIndex < words.length) {
        const currentWord = words[wordSearchIndex];

        if (currentWord.text.trim() === '') {
          wordSearchIndex++;
          continue;
        }

        const wordTextToFind = currentWord.text;
        const wordStartIndex = paragraph.indexOf(wordTextToFind, lastParagraphIndex);

        if (wordStartIndex === -1) {
          break;
        }

        if (wordStartIndex > lastParagraphIndex) {
          paragraphNodes.push(
            <span key={`p${pIndex}-text-${wordSearchIndex}`}>
              {paragraph.substring(lastParagraphIndex, wordStartIndex)}
            </span>,
          );
        }

        const titleText = `${currentWord.start.toFixed(2)}s - ${currentWord.end.toFixed(2)}s`;
        const isCurrentWord = wordSearchIndex === currentWordIndex;
        const isSelectedWord = wordSearchIndex === selectedWordIndex;

        const wordWithIndex: ExtendedWord = {
          ...currentWord,
          originalIndex: wordSearchIndex,
        };

        paragraphNodes.push(
          <span
            key={`p${pIndex}-word-${wordSearchIndex}`}
            className={cn(
              'cursor-pointer border-b-2 border-dotted border-primary/30 bg-primary/5 hover:bg-primary/10 hover:border-primary hover:border-solid transition-all px-0.5 mx-0.5 rounded-sm',
              isCurrentWord && 'bg-primary/15 border-primary/60 border-solid',
              isSelectedWord &&
                'bg-primary/20 border-primary border-solid ring-1 ring-primary/30',
            )}
            onClick={(e) => handleSelectWord(wordWithIndex, wordSearchIndex, e)}
            title={titleText}
          >
            {wordTextToFind}
          </span>,
        );

        lastParagraphIndex = wordStartIndex + wordTextToFind.length;
        wordSearchIndex++;
      }

      if (lastParagraphIndex < paragraph.length) {
        paragraphNodes.push(
          <span key={`p${pIndex}-text-end`}>
            {paragraph.substring(lastParagraphIndex)}
          </span>,
        );
      }

      if (paragraphNodes.length > 0) {
        finalParagraphs.push(
          <p key={`para-${pIndex}`} className="mb-4 whitespace-pre-wrap">
            {paragraphNodes}
          </p>,
        );
      }
    });

    return finalParagraphs;
  }, [content, words, currentWordIndex, selectedWordIndex]);

  return (
    <>
      <Card className="flex-1">
        {title && (
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg">{title}</CardTitle>
              {audioUrl && (
                <div className="flex items-center gap-3">
                  <div className="text-sm text-muted-foreground">
                    {formatTime(currentTime)} / {formatTime(duration)}
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={toggle}
                    className="h-8 w-8 p-0"
                    title={isPlaying ? 'Pause' : 'Play'}
                  >
                    {isPlaying ? (
                      <PauseIcon className="h-4 w-4" />
                    ) : (
                      <PlayIcon className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              )}
            </div>
          </CardHeader>
        )}
        {!title && audioUrl && (
          <div className="flex justify-end items-center p-2">
            <div className="flex items-center gap-3">
              <div className="text-sm text-muted-foreground">
                {formatTime(currentTime)} / {formatTime(duration)}
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={toggle}
                className="h-8 w-8 p-0"
                title={isPlaying ? 'Pause' : 'Play'}
              >
                {isPlaying ? (
                  <PauseIcon className="h-4 w-4" />
                ) : (
                  <PlayIcon className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        )}
        <CardContent>
          <ScrollArea
            ref={panelRef}
            className="h-[calc(100vh-23rem)] md:h-[calc(100vh-300px)] max-h-[calc(100vh-200px)]"
          >
            <div ref={scrollViewportRef}>
              <div
                ref={textContentRef}
                className="pr-4 text-content-wrapper"
                style={{ fontSize: `${fontSize}px` }}
              >
                {renderedContent}
              </div>
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      {selectedWordIndex !== null && words[selectedWordIndex] && (
        <Popover
          open={selectedWordIndex !== null}
          onOpenChange={(open: boolean) => {
            if (!open) setSelectedWordIndex(null);
          }}
        >
          {selectedWordElement && (
            <PopoverAnchor asChild>
              <span
                className="absolute"
                style={{
                  top: selectedWordElement.getBoundingClientRect().top,
                  left: selectedWordElement.getBoundingClientRect().left,
                  height: 0,
                  width: 0,
                  position: 'fixed',
                }}
              />
            </PopoverAnchor>
          )}
          <PopoverContent
            className="w-60 p-2"
            onKeyDown={handleKeyDown}
            side="top"
            align="center"
            sideOffset={5}
            avoidCollisions
          >
            <div className="grid gap-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1">
                  <span className="text-xs font-medium">{editWordText}</span>
                  <span className="text-xs text-muted-foreground">
                    ({parseFloat(editStartTime).toFixed(2)}s -{' '}
                    {parseFloat(editEndTime).toFixed(2)}s)
                  </span>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-5 w-5"
                  onClick={() => setSelectedWordIndex(null)}
                >
                  <XIcon className="h-3 w-3" />
                </Button>
              </div>

              <div className="grid grid-cols-2 gap-2">
                <div className="grid gap-1">
                  <Label htmlFor="start-time" className="text-xs text-muted-foreground">
                    Start
                  </Label>
                  <Input
                    id="start-time"
                    type="number"
                    step="0.01"
                    min="0"
                    value={editStartTime}
                    onChange={(e) => setEditStartTime(e.target.value)}
                    className="h-7 text-xs"
                  />
                </div>
                <div className="grid gap-1">
                  <Label htmlFor="end-time" className="text-xs text-muted-foreground">
                    End
                  </Label>
                  <Input
                    id="end-time"
                    type="number"
                    step="0.01"
                    min="0"
                    value={editEndTime}
                    onChange={(e) => setEditEndTime(e.target.value)}
                    className="h-7 text-xs"
                  />
                </div>
              </div>

              <div className="flex items-center justify-between gap-2 pt-1">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handlePlayWord}
                  className="h-6 px-2 text-xs"
                >
                  <PlayIcon className="h-3 w-3 mr-1" />
                  Play
                </Button>
                <Button
                  type="submit"
                  size="sm"
                  onClick={handleSaveEdit}
                  className="h-6 px-2 text-xs"
                >
                  <CheckIcon className="h-3 w-3 mr-1" />
                  Save
                </Button>
              </div>
            </div>
          </PopoverContent>
        </Popover>
      )}
    </>
  );
});
