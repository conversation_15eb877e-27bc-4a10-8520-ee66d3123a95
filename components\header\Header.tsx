import Image from 'next/image';
import Link from 'next/link';
import { MapIcon, BookOpenIcon, ScrollIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ThemeToggle } from '@/components/header/ThemeToggle';
import { MobileMenu } from '@/components/header/MobileMenu';

export interface HeaderProps {
  onImportStoryClick?: () => void;
}

export const Header = ({ onImportStoryClick }: HeaderProps) => {
  return (
    <header className="fixed top-0 left-0 right-0 bg-background border-b-2 z-10">
      <div className="max-w-7xl h-[72px] mx-auto px-4 sm:px-6 flex justify-between items-center">
        <Link href="/">
          <div className="flex items-center gap-2">
            <Image
              src="/icons/osemjeden.png"
              alt="Osem Jeden Logo"
              width={40}
              height={40}
            />
            <h1 className="text-lg md:text-2xl font-semibold">Osem Jeden</h1>
          </div>
        </Link>
        <div className="flex items-center gap-3 sm:gap-4">
          <div className="hidden sm:flex items-center gap-4">
            <Link
              href="/roadmap"
              className="flex items-center gap-2 text-sm hover:text-primary transition-colors duration-300"
            >
              <MapIcon size={16} />
              Roadmap
            </Link>
            <Link
              href="/historia"
              className="flex items-center gap-2 text-sm hover:text-primary transition-colors duration-300"
            >
              <ScrollIcon size={16} />
              História
            </Link>
            <Link
              href="/komiksy"
              className="flex items-center gap-2 text-sm hover:text-primary transition-colors duration-300"
            >
              <BookOpenIcon size={16} />
              Komiksy
            </Link>
            {/* <Link
              href="https://github.com/Xurify"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 text-sm hover:text-primary transition-colors duration-300"
            >
              <GithubIcon size={16} />
              GitHub
            </Link> */}
            {onImportStoryClick && (
              <Button onClick={onImportStoryClick} variant="default">
                Import Story
              </Button>
            )}
          </div>
          <ThemeToggle />
          <MobileMenu onImportStoryClick={onImportStoryClick} />
        </div>
      </div>
    </header>
  );
};
