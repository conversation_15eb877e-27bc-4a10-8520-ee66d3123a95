import { z } from 'zod';

export const TextAlignment = z.object({
  text: z.string(),
  start: z.number(),
  end: z.number(),
});

export type WordAlignment = z.infer<typeof TextAlignment>;

export const CharactersAlignmentSchema = z.object({
  characters: z.array(z.string()),
  character_start_times_seconds: z.array(z.number()),
  character_end_times_seconds: z.array(z.number()),
});
export type CharactersAlignment = z.infer<typeof CharactersAlignmentSchema>;

export const VoiceSettingsSchema = z.object({
  stability: z.number().optional(),
  similarity_boost: z.number().optional(),
  style: z.number().optional(),
  use_speaker_boost: z.boolean().optional(),
  speed: z.number().optional(),
});

export const GenerateAudioRequestBodySchema = z.object({
  text: z.string(),
  voice_id: z.string(),
  voice_settings: VoiceSettingsSchema,
});
export type GenerateAudioRequestBody = z.infer<typeof GenerateAudioRequestBodySchema>;

export const GenerateAudioResponseBodySchema = z.object({
  alignment: CharactersAlignmentSchema,
  normalized_alignment: CharactersAlignmentSchema,
  audio_base64: z.string(),
});
export type GenerateResponseBody = z.infer<typeof GenerateAudioResponseBodySchema>;

export const GenerateWordsRequestBodySchema = z.object({
  text: z.string(),
  audio_file: z.object({}),
});
export type GenerateWordsRequestBody = z.infer<typeof GenerateWordsRequestBodySchema>;

export const GenerateWordsResponseBodySchema = z.object({
  text: z.string(),
  words: z.array(TextAlignment),
  character: z.array(TextAlignment),
});
export type GenerateWordsResponseBody = z.infer<typeof GenerateWordsResponseBodySchema>;

export const ConvertRequestBodySchema = z.object({
  alignment: CharactersAlignmentSchema,
});
export type ConvertRequestBody = z.infer<typeof ConvertRequestBodySchema>;

export const ConvertResponseBodySchema = z.object({
  text: z.string(),
  words: z.array(TextAlignment),
});
export type ConvertResponseBody = z.infer<typeof ConvertResponseBodySchema>;

export const ApiErrorResponseSchema = z.object({
  error: z.string(),
});
export type ApiErrorResponse = z.infer<typeof ApiErrorResponseSchema>;
