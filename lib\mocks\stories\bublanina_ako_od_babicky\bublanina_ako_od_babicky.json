{"id": "bublanina-ako-od-babicky", "title": "Bublanina a<PERSON> od <PERSON>", "english_title": "<PERSON><PERSON><PERSON><PERSON> Like Grandma's", "audio_url": "https://x9xfaca7mb.ufs.sh/f/TuGkI0HClmz3bMGGxf8fEK9jC58PYIyTekmtBZXMRdNVOchA", "image_url": "/images/stories/bublanina_ako_od_babicky.png", "voice_id": "Xb7hH8MSUJpSbSDYk0k2", "description": "Daždivá nedeľa inšpiruje Janku upiecť nadýchaný koláč podľa receptu babičky, ktorý prinesie spomienky.", "dictionary": [], "text": "Bublanina ako od babičky\n\nDnes je nedeľa. Janka je doma a má chuť na niečo sladké. Vonku prší a Jan<PERSON> pre<PERSON>, čo robiť. Zrazu si spomenie na svoju babičku. Babička vždy v nedeľu piekla bublaninu.\n\n\"To je dobrý nápad!\" povie Janka. \"Upečiem bublaninu ako moja babička.\"\n\nJanka ide do kuchyne a vyberá ingrediencie: vajcia, cukor, múku, olej a mlieko. V chladničke nájde čerstvé čerešne, ktoré kúpila včera na trhu.\n\n\"Bublanina s čerešňami je najlepšia,\" hovorí si Janka.\n\nNajprv rozdelí vajcia na žĺtky a bielky. Z bielkov a štipky soli vyšľahá tuhý sneh. \"Babička v<PERSON>dy hovorila, že sneh musí byť taký tuhý, aby sa miska mohla obrátiť hore dnom a sneh nevypadne,\" spomína si Janka.\n\nPotom v druhej miske vyšľahá žĺtky s cukrom. Pridá olej, mlieko a múku s práškom do pečiva. Na koniec jemne primieša sneh z bielkov.\n\n\"Toto je tajomstvo babičkinej bublaniny,\" hovorí Janka. \"Sneh robí koláč ľahký a nadýchaný ako obláčik.\"\n\nJanka naleje cesto na plech a na vrch uloží čerešne. Trochu ich zatlačí do cesta. Potom dá plech do rúry a pečie 30 minút.\n\nCelý byt vonia. Tá vôňa pripomína Janke detstvo a nedeľné popoludnia u babičky. Keď je koláč hotový, Janka ho vyberie z rúry. Je krásne zlatý. Po vychladnutí ho posype práškovým cukrom.\n\nJanka si sadne ku oknu s kúskom bublaniny a šálkou kávy. Vonku stále prší, ale v byte je teplo a príjemne. Prvé sústo je úžasné – koláč je mäkký, nadýchaný a čerešne sú sladké.\n\n\"Presne ako od babičky,\" povie Janka spokojne. \"Na jeseň skúsim bublaninu so slivkami a v zime s mrazenými malinami.\"\n\nJanka je šťastná. Dnes sa naučila pripraviť tradičný slovenský koláč – bublaninu, ktorá nikdy nesklame.", "translation": "B<PERSON>lanina Like Grandma's\n\nToday is Sunday. <PERSON><PERSON> is at home and craves something sweet. It's raining outside and <PERSON><PERSON> is thinking about what to do. Suddenly she remembers her grandmother. <PERSON>mother always baked bublanina on Sundays.\n\n\"That's a good idea!\" says <PERSON><PERSON>. \"I'll bake bublanina like my grandmother.\"\n\n<PERSON><PERSON> goes to the kitchen and takes out the ingredients: eggs, sugar, flour, oil, and milk. In the refrigerator, she finds fresh cherries that she bought yesterday at the market.\n\n\"Cherry bublanina is the best,\" <PERSON><PERSON> says to herself.\n\nFirst, she separates the eggs into yolks and whites. She beats the egg whites with a pinch of salt until stiff peaks form. \"<PERSON><PERSON> always said that the beaten whites should be so stiff that you can turn the bowl upside down and they won't fall out,\" <PERSON><PERSON> remembers.\n\nThen in another bowl, she beats the egg yolks with sugar. She adds oil, milk, and flour with baking powder. Finally, she gently folds in the beaten egg whites.\n\n\"This is the secret of grandmother's bublanina,\" says <PERSON><PERSON>. \"The beaten whites make the cake light and fluffy like a cloud.\"\n\n<PERSON><PERSON> pours the batter onto a baking sheet and places cherries on top. She presses them slightly into the batter. Then she puts the tray in the oven and bakes it for 30 minutes.\n\nThe whole apartment smells wonderful. That aroma reminds <PERSON><PERSON> of her childhood and Sunday afternoons at her grandmother's. When the cake is ready, <PERSON><PERSON> takes it out of the oven. It's beautifully golden. After cooling, she dusts it with powdered sugar.\n\n<PERSON><PERSON> sits by the window with a piece of bublanina and a cup of coffee. It's still raining outside, but it's warm and cozy in the apartment. The first bite is amazing – the cake is soft, fluffy, and the cherries are sweet.\n\n\"Just like grandmother's,\" <PERSON>ka says contentedly. \"In autumn I'll try bublanina with plums and in winter with frozen raspberries.\"\n\n<PERSON>ka is happy. Today she learned how to prepare a traditional Slovak cake – bublanina, which never disappoints.", "words": [{"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 0.119, "end": 0.68}, {"text": " ", "start": 0.68, "end": 0.699}, {"text": "ako", "start": 0.699, "end": 0.979}, {"text": " ", "start": 0.979, "end": 1.12}, {"text": "od", "start": 1.12, "end": 1.279}, {"text": " ", "start": 1.279, "end": 1.319}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 1.319, "end": 1.879}, {"text": "\n", "start": 1.879, "end": 3.859}, {"text": "\n", "start": 3.859, "end": 3.899}, {"text": "Dnes", "start": 3.899, "end": 4.299}, {"text": " ", "start": 4.299, "end": 4.339}, {"text": "je", "start": 4.339, "end": 4.48}, {"text": " ", "start": 4.48, "end": 4.519}, {"text": "<PERSON><PERSON><PERSON><PERSON>.", "start": 4.519, "end": 5.119}, {"text": " ", "start": 5.119, "end": 5.539}, {"text": "<PERSON><PERSON>", "start": 5.539, "end": 5.879}, {"text": " ", "start": 5.879, "end": 5.9}, {"text": "je", "start": 5.9, "end": 6.0}, {"text": " ", "start": 6.0, "end": 6.019}, {"text": "doma", "start": 6.019, "end": 6.299}, {"text": " ", "start": 6.299, "end": 6.339}, {"text": "a", "start": 6.339, "end": 6.399}, {"text": " ", "start": 6.399, "end": 6.46}, {"text": "má", "start": 6.46, "end": 6.639}, {"text": " ", "start": 6.639, "end": 6.659}, {"text": "chuť", "start": 6.659, "end": 6.92}, {"text": " ", "start": 6.92, "end": 6.94}, {"text": "na", "start": 6.94, "end": 7.059}, {"text": " ", "start": 7.059, "end": 7.099}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 7.099, "end": 7.399}, {"text": " ", "start": 7.399, "end": 7.419}, {"text": "sladké.", "start": 7.419, "end": 7.899}, {"text": " ", "start": 7.899, "end": 8.319}, {"text": "<PERSON><PERSON>", "start": 8.319, "end": 8.599}, {"text": " ", "start": 8.599, "end": 8.699}, {"text": "prší", "start": 8.699, "end": 9.019}, {"text": " ", "start": 9.019, "end": 9.139}, {"text": "a", "start": 9.139, "end": 9.26}, {"text": " ", "start": 9.26, "end": 9.3}, {"text": "<PERSON><PERSON>", "start": 9.3, "end": 9.579}, {"text": " ", "start": 9.579, "end": 9.679}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,", "start": 9.679, "end": 10.159}, {"text": " ", "start": 10.159, "end": 10.26}, {"text": "čo", "start": 10.26, "end": 10.399}, {"text": " ", "start": 10.399, "end": 10.439}, {"text": "robiť.", "start": 10.439, "end": 10.84}, {"text": " ", "start": 10.84, "end": 11.42}, {"text": "<PERSON><PERSON><PERSON>", "start": 11.42, "end": 11.699}, {"text": " ", "start": 11.699, "end": 11.719}, {"text": "si", "start": 11.719, "end": 11.839}, {"text": " ", "start": 11.839, "end": 11.88}, {"text": "spomenie", "start": 11.88, "end": 12.3}, {"text": " ", "start": 12.3, "end": 12.34}, {"text": "na", "start": 12.34, "end": 12.46}, {"text": " ", "start": 12.46, "end": 12.5}, {"text": "svoju", "start": 12.5, "end": 12.739}, {"text": " ", "start": 12.739, "end": 12.779}, {"text": "babičku.", "start": 12.779, "end": 13.299}, {"text": " ", "start": 13.299, "end": 13.859}, {"text": "Babička", "start": 13.859, "end": 14.339}, {"text": " ", "start": 14.339, "end": 14.38}, {"text": "vždy", "start": 14.38, "end": 14.599}, {"text": " ", "start": 14.599, "end": 14.639}, {"text": "v", "start": 14.639, "end": 14.679}, {"text": " ", "start": 14.679, "end": 14.719}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 14.719, "end": 15.159}, {"text": " ", "start": 15.159, "end": 15.219}, {"text": "piekla", "start": 15.219, "end": 15.619}, {"text": " ", "start": 15.619, "end": 15.679}, {"text": "<PERSON><PERSON><PERSON><PERSON>.", "start": 15.679, "end": 16.299}, {"text": "\n", "start": 16.299, "end": 16.799}, {"text": "\n", "start": 16.799, "end": 16.819}, {"text": "\"To", "start": 16.819, "end": 16.92}, {"text": " ", "start": 16.92, "end": 16.979}, {"text": "je", "start": 16.979, "end": 17.1}, {"text": " ", "start": 17.1, "end": 17.159}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 17.159, "end": 17.52}, {"text": " ", "start": 17.52, "end": 17.559}, {"text": "nápad!\"", "start": 17.559, "end": 18.1}, {"text": " ", "start": 18.1, "end": 18.42}, {"text": "povie", "start": 18.42, "end": 18.68}, {"text": " ", "start": 18.68, "end": 18.739}, {"text": "<PERSON><PERSON>.", "start": 18.739, "end": 19.159}, {"text": " ", "start": 19.159, "end": 19.659}, {"text": "\"Upečiem", "start": 19.659, "end": 20.219}, {"text": " ", "start": 20.219, "end": 20.319}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 20.319, "end": 20.879}, {"text": " ", "start": 20.879, "end": 20.939}, {"text": "ako", "start": 20.939, "end": 21.159}, {"text": " ", "start": 21.159, "end": 21.18}, {"text": "moja", "start": 21.18, "end": 21.459}, {"text": " ", "start": 21.459, "end": 21.5}, {"text": "b<PERSON><PERSON><PERSON>.\"", "start": 21.5, "end": 22.159}, {"text": "\n", "start": 22.159, "end": 22.819}, {"text": "\n", "start": 22.819, "end": 22.859}, {"text": "<PERSON><PERSON>", "start": 22.859, "end": 23.159}, {"text": " ", "start": 23.159, "end": 23.199}, {"text": "ide", "start": 23.199, "end": 23.34}, {"text": " ", "start": 23.34, "end": 23.379}, {"text": "do", "start": 23.379, "end": 23.439}, {"text": " ", "start": 23.439, "end": 23.519}, {"text": "kuchyne", "start": 23.519, "end": 23.939}, {"text": " ", "start": 23.939, "end": 24.0}, {"text": "a", "start": 24.0, "end": 24.059}, {"text": " ", "start": 24.059, "end": 24.1}, {"text": "vyberá", "start": 24.1, "end": 24.5}, {"text": " ", "start": 24.5, "end": 24.6}, {"text": "ingrediencie:", "start": 24.6, "end": 25.399}, {"text": " ", "start": 25.399, "end": 25.899}, {"text": "vajcia,", "start": 25.899, "end": 26.399}, {"text": " ", "start": 26.399, "end": 26.599}, {"text": "cu<PERSON>,", "start": 26.599, "end": 27.0}, {"text": " ", "start": 27.0, "end": 27.299}, {"text": "<PERSON><PERSON><PERSON>,", "start": 27.299, "end": 27.719}, {"text": " ", "start": 27.719, "end": 28.019}, {"text": "<PERSON><PERSON><PERSON>", "start": 28.019, "end": 28.419}, {"text": " ", "start": 28.419, "end": 28.479}, {"text": "a", "start": 28.479, "end": 28.539}, {"text": " ", "start": 28.539, "end": 28.599}, {"text": "mlieko.", "start": 28.599, "end": 29.059}, {"text": " ", "start": 29.059, "end": 29.679}, {"text": "V", "start": 29.679, "end": 29.699}, {"text": " ", "start": 29.699, "end": 29.719}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 29.719, "end": 30.279}, {"text": " ", "start": 30.279, "end": 30.319}, {"text": "n<PERSON><PERSON><PERSON>", "start": 30.319, "end": 30.659}, {"text": " ", "start": 30.659, "end": 30.739}, {"text": "čerstvé", "start": 30.739, "end": 31.179}, {"text": " ", "start": 31.179, "end": 31.219}, {"text": "<PERSON><PERSON><PERSON><PERSON>,", "start": 31.219, "end": 31.699}, {"text": " ", "start": 31.699, "end": 31.799}, {"text": "ktor<PERSON>", "start": 31.799, "end": 32.079}, {"text": " ", "start": 32.079, "end": 32.139}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 32.139, "end": 32.54}, {"text": " ", "start": 32.54, "end": 32.579}, {"text": "včera", "start": 32.579, "end": 32.939}, {"text": " ", "start": 32.939, "end": 32.979}, {"text": "na", "start": 32.979, "end": 33.079}, {"text": " ", "start": 33.079, "end": 33.159}, {"text": "trhu.", "start": 33.159, "end": 33.54}, {"text": "\n", "start": 33.54, "end": 34.34}, {"text": "\n", "start": 34.34, "end": 34.36}, {"text": "\"<PERSON><PERSON><PERSON><PERSON>", "start": 34.36, "end": 34.959}, {"text": " ", "start": 34.959, "end": 35.02}, {"text": "s", "start": 35.02, "end": 35.04}, {"text": " ", "start": 35.04, "end": 35.119}, {"text": "čerešňami", "start": 35.119, "end": 35.639}, {"text": " ", "start": 35.639, "end": 35.68}, {"text": "je", "start": 35.68, "end": 35.779}, {"text": " ", "start": 35.779, "end": 35.919}, {"text": "na<PERSON><PERSON><PERSON><PERSON><PERSON>,\"", "start": 35.919, "end": 36.54}, {"text": " ", "start": 36.54, "end": 36.819}, {"text": "hovorí", "start": 36.819, "end": 37.159}, {"text": " ", "start": 37.159, "end": 37.2}, {"text": "si", "start": 37.2, "end": 37.279}, {"text": " ", "start": 37.279, "end": 37.34}, {"text": "<PERSON><PERSON>.", "start": 37.34, "end": 37.719}, {"text": "\n", "start": 37.719, "end": 38.34}, {"text": "\n", "start": 38.34, "end": 38.399}, {"text": "Najprv", "start": 38.399, "end": 38.759}, {"text": " ", "start": 38.759, "end": 38.819}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 38.819, "end": 39.279}, {"text": " ", "start": 39.279, "end": 39.34}, {"text": "vajcia", "start": 39.34, "end": 39.759}, {"text": " ", "start": 39.759, "end": 39.819}, {"text": "na", "start": 39.819, "end": 39.919}, {"text": " ", "start": 39.919, "end": 40.0}, {"text": "žĺtky", "start": 40.0, "end": 40.459}, {"text": " ", "start": 40.459, "end": 40.52}, {"text": "a", "start": 40.52, "end": 40.619}, {"text": " ", "start": 40.619, "end": 40.659}, {"text": "bielky.", "start": 40.659, "end": 41.2}, {"text": " ", "start": 41.2, "end": 41.659}, {"text": "Z", "start": 41.659, "end": 41.719}, {"text": " ", "start": 41.719, "end": 41.759}, {"text": "<PERSON><PERSON><PERSON>", "start": 41.759, "end": 42.259}, {"text": " ", "start": 42.259, "end": 42.319}, {"text": "a", "start": 42.319, "end": 42.399}, {"text": " ", "start": 42.399, "end": 42.459}, {"text": "<PERSON><PERSON><PERSON>", "start": 42.459, "end": 42.879}, {"text": " ", "start": 42.879, "end": 42.979}, {"text": "soli", "start": 42.979, "end": 43.459}, {"text": " ", "start": 43.459, "end": 43.559}, {"text": "vyšľahá", "start": 43.559, "end": 44.119}, {"text": " ", "start": 44.119, "end": 44.299}, {"text": "tuh<PERSON>", "start": 44.299, "end": 44.619}, {"text": " ", "start": 44.619, "end": 44.7}, {"text": "sneh.", "start": 44.7, "end": 45.119}, {"text": " ", "start": 45.119, "end": 45.539}, {"text": "\"Babička", "start": 45.539, "end": 46.0}, {"text": " ", "start": 46.0, "end": 46.059}, {"text": "vždy", "start": 46.059, "end": 46.299}, {"text": " ", "start": 46.299, "end": 46.34}, {"text": "hovor<PERSON>,", "start": 46.34, "end": 46.84}, {"text": " ", "start": 46.84, "end": 46.899}, {"text": "že", "start": 46.899, "end": 47.019}, {"text": " ", "start": 47.019, "end": 47.079}, {"text": "sneh", "start": 47.079, "end": 47.319}, {"text": " ", "start": 47.319, "end": 47.36}, {"text": "musí", "start": 47.36, "end": 47.579}, {"text": " ", "start": 47.579, "end": 47.619}, {"text": "byť", "start": 47.619, "end": 47.799}, {"text": " ", "start": 47.799, "end": 47.879}, {"text": "tak<PERSON>", "start": 47.879, "end": 48.239}, {"text": " ", "start": 48.239, "end": 48.299}, {"text": "<PERSON><PERSON><PERSON>,", "start": 48.299, "end": 48.939}, {"text": " ", "start": 48.939, "end": 49.039}, {"text": "aby", "start": 49.039, "end": 49.2}, {"text": " ", "start": 49.2, "end": 49.259}, {"text": "sa", "start": 49.259, "end": 49.36}, {"text": " ", "start": 49.36, "end": 49.459}, {"text": "miska", "start": 49.459, "end": 49.819}, {"text": " ", "start": 49.819, "end": 49.86}, {"text": "mohla", "start": 49.86, "end": 50.119}, {"text": " ", "start": 50.119, "end": 50.2}, {"text": "obrátiť", "start": 50.2, "end": 50.659}, {"text": " ", "start": 50.659, "end": 50.719}, {"text": "hore", "start": 50.719, "end": 50.939}, {"text": " ", "start": 50.939, "end": 51.0}, {"text": "dnom", "start": 51.0, "end": 51.619}, {"text": " ", "start": 51.619, "end": 51.699}, {"text": "a", "start": 51.699, "end": 51.779}, {"text": " ", "start": 51.779, "end": 51.84}, {"text": "sneh", "start": 51.84, "end": 52.18}, {"text": " ", "start": 52.18, "end": 52.239}, {"text": "<PERSON><PERSON><PERSON><PERSON>,\"", "start": 52.239, "end": 53.039}, {"text": " ", "start": 53.039, "end": 53.279}, {"text": "spo<PERSON><PERSON>a", "start": 53.279, "end": 53.739}, {"text": " ", "start": 53.739, "end": 53.799}, {"text": "si", "start": 53.799, "end": 53.879}, {"text": " ", "start": 53.879, "end": 53.939}, {"text": "<PERSON><PERSON>.", "start": 53.939, "end": 54.439}, {"text": "\n", "start": 54.439, "end": 54.879}, {"text": "\n", "start": 54.879, "end": 54.919}, {"text": "<PERSON><PERSON>", "start": 54.919, "end": 55.199}, {"text": " ", "start": 55.199, "end": 55.239}, {"text": "v", "start": 55.239, "end": 55.299}, {"text": " ", "start": 55.299, "end": 55.34}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 55.34, "end": 55.639}, {"text": " ", "start": 55.639, "end": 55.68}, {"text": "miske", "start": 55.68, "end": 56.02}, {"text": " ", "start": 56.02, "end": 56.079}, {"text": "vyšľahá", "start": 56.079, "end": 56.619}, {"text": " ", "start": 56.619, "end": 56.719}, {"text": "žĺtky", "start": 56.719, "end": 57.139}, {"text": " ", "start": 57.139, "end": 57.199}, {"text": "s", "start": 57.199, "end": 57.239}, {"text": " ", "start": 57.239, "end": 57.319}, {"text": "cukrom.", "start": 57.319, "end": 57.859}, {"text": " ", "start": 57.859, "end": 58.259}, {"text": "Pridá", "start": 58.259, "end": 58.659}, {"text": " ", "start": 58.659, "end": 58.779}, {"text": "<PERSON><PERSON><PERSON>,", "start": 58.779, "end": 59.059}, {"text": " ", "start": 59.059, "end": 59.159}, {"text": "mlieko", "start": 59.159, "end": 59.739}, {"text": " ", "start": 59.739, "end": 59.84}, {"text": "a", "start": 59.84, "end": 59.939}, {"text": " ", "start": 59.939, "end": 60.02}, {"text": "múku", "start": 60.02, "end": 60.419}, {"text": " ", "start": 60.419, "end": 60.479}, {"text": "s", "start": 60.479, "end": 60.559}, {"text": " ", "start": 60.559, "end": 60.619}, {"text": "práškom", "start": 60.619, "end": 61.139}, {"text": " ", "start": 61.139, "end": 61.18}, {"text": "do", "start": 61.18, "end": 61.279}, {"text": " ", "start": 61.279, "end": 61.34}, {"text": "pečiva.", "start": 61.34, "end": 61.879}, {"text": " ", "start": 61.879, "end": 62.379}, {"text": "Na", "start": 62.379, "end": 62.5}, {"text": " ", "start": 62.5, "end": 62.539}, {"text": "koniec", "start": 62.539, "end": 62.939}, {"text": " ", "start": 62.939, "end": 63.0}, {"text": "j<PERSON>ne", "start": 63.0, "end": 63.319}, {"text": " ", "start": 63.319, "end": 63.359}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 63.359, "end": 63.859}, {"text": " ", "start": 63.859, "end": 63.939}, {"text": "sneh", "start": 63.939, "end": 64.239}, {"text": " ", "start": 64.239, "end": 64.299}, {"text": "z", "start": 64.299, "end": 64.339}, {"text": " ", "start": 64.339, "end": 64.4}, {"text": "bielkov.", "start": 64.4, "end": 65.08}, {"text": "\n", "start": 65.08, "end": 65.76}, {"text": "\n", "start": 65.76, "end": 65.779}, {"text": "\"Toto", "start": 65.779, "end": 66.099}, {"text": " ", "start": 66.099, "end": 66.159}, {"text": "je", "start": 66.159, "end": 66.279}, {"text": " ", "start": 66.279, "end": 66.319}, {"text": "tajomstvo", "start": 66.319, "end": 66.979}, {"text": " ", "start": 66.979, "end": 67.04}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 67.04, "end": 67.76}, {"text": " ", "start": 67.76, "end": 67.819}, {"text": "bublaniny,\"", "start": 67.819, "end": 68.819}, {"text": " ", "start": 68.819, "end": 68.879}, {"text": "hovorí", "start": 68.879, "end": 69.239}, {"text": " ", "start": 69.239, "end": 69.299}, {"text": "<PERSON><PERSON>.", "start": 69.299, "end": 69.839}, {"text": " ", "start": 69.839, "end": 70.319}, {"text": "\"Sneh", "start": 70.319, "end": 70.759}, {"text": " ", "start": 70.759, "end": 70.819}, {"text": "rob<PERSON>", "start": 70.819, "end": 71.099}, {"text": " ", "start": 71.099, "end": 71.159}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 71.159, "end": 71.639}, {"text": " ", "start": 71.639, "end": 71.68}, {"text": "ľahký", "start": 71.68, "end": 72.319}, {"text": " ", "start": 72.319, "end": 72.4}, {"text": "a", "start": 72.4, "end": 72.459}, {"text": " ", "start": 72.459, "end": 72.519}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 72.519, "end": 73.259}, {"text": " ", "start": 73.259, "end": 73.36}, {"text": "ako", "start": 73.36, "end": 73.619}, {"text": " ", "start": 73.619, "end": 73.72}, {"text": "obláčik.\"", "start": 73.72, "end": 74.54}, {"text": "\n", "start": 74.54, "end": 75.139}, {"text": "\n", "start": 75.139, "end": 75.22}, {"text": "<PERSON><PERSON>", "start": 75.22, "end": 75.539}, {"text": " ", "start": 75.539, "end": 75.599}, {"text": "<PERSON><PERSON><PERSON>", "start": 75.599, "end": 75.9}, {"text": " ", "start": 75.9, "end": 75.979}, {"text": "cesto", "start": 75.979, "end": 76.279}, {"text": " ", "start": 76.279, "end": 76.319}, {"text": "na", "start": 76.319, "end": 76.459}, {"text": " ", "start": 76.459, "end": 76.5}, {"text": "plech", "start": 76.5, "end": 76.839}, {"text": " ", "start": 76.839, "end": 76.919}, {"text": "a", "start": 76.919, "end": 76.979}, {"text": " ", "start": 76.979, "end": 77.08}, {"text": "na", "start": 77.08, "end": 77.199}, {"text": " ", "start": 77.199, "end": 77.239}, {"text": "vrch", "start": 77.239, "end": 77.479}, {"text": " ", "start": 77.479, "end": 77.559}, {"text": "ul<PERSON><PERSON><PERSON>", "start": 77.559, "end": 77.919}, {"text": " ", "start": 77.919, "end": 77.979}, {"text": "<PERSON><PERSON><PERSON><PERSON>.", "start": 77.979, "end": 78.579}, {"text": " ", "start": 78.579, "end": 79.019}, {"text": "<PERSON><PERSON><PERSON>", "start": 79.019, "end": 79.279}, {"text": " ", "start": 79.279, "end": 79.339}, {"text": "ich", "start": 79.339, "end": 79.439}, {"text": " ", "start": 79.439, "end": 79.5}, {"text": "zatlačí", "start": 79.5, "end": 79.999}, {"text": " ", "start": 79.999, "end": 80.04}, {"text": "do", "start": 80.04, "end": 80.119}, {"text": " ", "start": 80.119, "end": 80.199}, {"text": "cesta.", "start": 80.199, "end": 80.619}, {"text": " ", "start": 80.619, "end": 80.979}, {"text": "<PERSON><PERSON>", "start": 80.979, "end": 81.239}, {"text": " ", "start": 81.239, "end": 81.299}, {"text": "dá", "start": 81.299, "end": 81.459}, {"text": " ", "start": 81.459, "end": 81.519}, {"text": "plech", "start": 81.519, "end": 81.739}, {"text": " ", "start": 81.739, "end": 81.799}, {"text": "do", "start": 81.799, "end": 81.9}, {"text": " ", "start": 81.9, "end": 81.959}, {"text": "<PERSON><PERSON><PERSON>", "start": 81.959, "end": 82.339}, {"text": " ", "start": 82.339, "end": 82.419}, {"text": "a", "start": 82.419, "end": 82.58}, {"text": " ", "start": 82.58, "end": 82.619}, {"text": "pečie", "start": 82.619, "end": 83.0}, {"text": " ", "start": 83.0, "end": 83.199}, {"text": "30", "start": 83.199, "end": 83.499}, {"text": " ", "start": 83.499, "end": 83.559}, {"text": "minút.", "start": 83.559, "end": 84.0}, {"text": "\n", "start": 84.0, "end": 84.36}, {"text": "\n", "start": 84.36, "end": 84.439}, {"text": "<PERSON><PERSON><PERSON>", "start": 84.439, "end": 84.699}, {"text": " ", "start": 84.699, "end": 84.759}, {"text": "byt", "start": 84.759, "end": 84.939}, {"text": " ", "start": 84.939, "end": 85.0}, {"text": "<PERSON><PERSON>.", "start": 85.0, "end": 85.879}, {"text": " ", "start": 85.879, "end": 85.939}, {"text": "Tá", "start": 85.939, "end": 86.059}, {"text": " ", "start": 86.059, "end": 86.119}, {"text": "vôňa", "start": 86.119, "end": 86.399}, {"text": " ", "start": 86.399, "end": 86.419}, {"text": "prip<PERSON><PERSON><PERSON>", "start": 86.419, "end": 86.959}, {"text": " ", "start": 86.959, "end": 87.019}, {"text": "<PERSON><PERSON>", "start": 87.019, "end": 87.379}, {"text": " ", "start": 87.379, "end": 87.419}, {"text": "detstvo", "start": 87.419, "end": 88.139}, {"text": " ", "start": 88.139, "end": 88.199}, {"text": "a", "start": 88.199, "end": 88.259}, {"text": " ", "start": 88.259, "end": 88.319}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 88.319, "end": 88.799}, {"text": " ", "start": 88.799, "end": 88.839}, {"text": "popoludnia", "start": 88.839, "end": 89.439}, {"text": " ", "start": 89.439, "end": 89.5}, {"text": "u", "start": 89.5, "end": 89.559}, {"text": " ", "start": 89.559, "end": 89.619}, {"text": "<PERSON><PERSON><PERSON><PERSON>.", "start": 89.619, "end": 90.219}, {"text": " ", "start": 90.219, "end": 90.659}, {"text": "Keď", "start": 90.659, "end": 90.799}, {"text": " ", "start": 90.799, "end": 90.839}, {"text": "je", "start": 90.839, "end": 90.919}, {"text": " ", "start": 90.919, "end": 90.959}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 90.959, "end": 91.279}, {"text": " ", "start": 91.279, "end": 91.319}, {"text": "<PERSON><PERSON><PERSON>,", "start": 91.319, "end": 92.019}, {"text": " ", "start": 92.019, "end": 92.099}, {"text": "<PERSON><PERSON>", "start": 92.099, "end": 92.419}, {"text": " ", "start": 92.419, "end": 92.459}, {"text": "ho", "start": 92.459, "end": 92.559}, {"text": " ", "start": 92.559, "end": 92.599}, {"text": "vyberie", "start": 92.599, "end": 92.979}, {"text": " ", "start": 92.979, "end": 93.059}, {"text": "z", "start": 93.059, "end": 93.099}, {"text": " ", "start": 93.099, "end": 93.159}, {"text": "<PERSON><PERSON><PERSON>.", "start": 93.159, "end": 93.5}, {"text": " ", "start": 93.5, "end": 93.839}, {"text": "Je", "start": 93.839, "end": 93.979}, {"text": " ", "start": 93.979, "end": 94.059}, {"text": "<PERSON>r<PERSON><PERSON>", "start": 94.059, "end": 94.599}, {"text": " ", "start": 94.599, "end": 94.68}, {"text": "<PERSON><PERSON><PERSON>.", "start": 94.68, "end": 95.199}, {"text": " ", "start": 95.199, "end": 95.68}, {"text": "Po", "start": 95.68, "end": 95.739}, {"text": " ", "start": 95.739, "end": 95.779}, {"text": "vychladnutí", "start": 95.779, "end": 96.479}, {"text": " ", "start": 96.479, "end": 96.519}, {"text": "ho", "start": 96.519, "end": 96.619}, {"text": " ", "start": 96.619, "end": 96.68}, {"text": "posype", "start": 96.68, "end": 97.099}, {"text": " ", "start": 97.099, "end": 97.159}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 97.159, "end": 97.739}, {"text": " ", "start": 97.739, "end": 97.819}, {"text": "cukrom.", "start": 97.819, "end": 98.299}, {"text": "\n", "start": 98.299, "end": 98.839}, {"text": "\n", "start": 98.839, "end": 98.879}, {"text": "<PERSON><PERSON>", "start": 98.879, "end": 99.179}, {"text": " ", "start": 99.179, "end": 99.22}, {"text": "si", "start": 99.22, "end": 99.319}, {"text": " ", "start": 99.319, "end": 99.379}, {"text": "sadne", "start": 99.379, "end": 99.739}, {"text": " ", "start": 99.739, "end": 99.759}, {"text": "ku", "start": 99.759, "end": 99.839}, {"text": " ", "start": 99.839, "end": 100.0}, {"text": "oknu", "start": 100.0, "end": 100.259}, {"text": " ", "start": 100.259, "end": 100.319}, {"text": "s", "start": 100.319, "end": 100.339}, {"text": " ", "start": 100.339, "end": 100.419}, {"text": "kúskom", "start": 100.419, "end": 100.819}, {"text": " ", "start": 100.819, "end": 100.86}, {"text": "bublaniny", "start": 100.86, "end": 101.599}, {"text": " ", "start": 101.599, "end": 101.619}, {"text": "a", "start": 101.619, "end": 101.719}, {"text": " ", "start": 101.719, "end": 101.799}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 101.799, "end": 102.219}, {"text": " ", "start": 102.219, "end": 102.299}, {"text": "k<PERSON><PERSON>.", "start": 102.299, "end": 102.699}, {"text": " ", "start": 102.699, "end": 103.199}, {"text": "<PERSON><PERSON>", "start": 103.199, "end": 103.539}, {"text": " ", "start": 103.539, "end": 103.619}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 103.619, "end": 103.939}, {"text": " ", "start": 103.939, "end": 104.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>,", "start": 104.0, "end": 104.68}, {"text": " ", "start": 104.68, "end": 104.759}, {"text": "ale", "start": 104.759, "end": 104.899}, {"text": " ", "start": 104.899, "end": 104.939}, {"text": "v", "start": 104.939, "end": 104.959}, {"text": " ", "start": 104.959, "end": 105.0}, {"text": "byte", "start": 105.0, "end": 105.219}, {"text": " ", "start": 105.219, "end": 105.259}, {"text": "je", "start": 105.259, "end": 105.399}, {"text": " ", "start": 105.399, "end": 105.439}, {"text": "teplo", "start": 105.439, "end": 106.159}, {"text": " ", "start": 106.159, "end": 106.18}, {"text": "a", "start": 106.18, "end": 106.199}, {"text": " ", "start": 106.199, "end": 106.279}, {"text": "príjemne.", "start": 106.279, "end": 107.119}, {"text": " ", "start": 107.119, "end": 107.18}, {"text": "Prvé", "start": 107.18, "end": 107.439}, {"text": " ", "start": 107.439, "end": 107.519}, {"text": "<PERSON><PERSON><PERSON>", "start": 107.519, "end": 107.859}, {"text": " ", "start": 107.859, "end": 107.919}, {"text": "je", "start": 107.919, "end": 108.099}, {"text": " ", "start": 108.099, "end": 108.22}, {"text": "úžasné", "start": 108.22, "end": 109.139}, {"text": " ", "start": 109.139, "end": 109.159}, {"text": "–", "start": 109.159, "end": 109.16000000000001}, {"text": " ", "start": 109.16000000000001, "end": 109.22}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 109.22, "end": 109.579}, {"text": " ", "start": 109.579, "end": 109.659}, {"text": "je", "start": 109.659, "end": 109.759}, {"text": " ", "start": 109.759, "end": 109.86}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>,", "start": 109.86, "end": 110.339}, {"text": " ", "start": 110.339, "end": 110.419}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 110.419, "end": 111.299}, {"text": " ", "start": 111.299, "end": 111.439}, {"text": "a", "start": 111.439, "end": 111.459}, {"text": " ", "start": 111.459, "end": 111.599}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 111.599, "end": 112.019}, {"text": " ", "start": 112.019, "end": 112.079}, {"text": "sú", "start": 112.079, "end": 112.199}, {"text": " ", "start": 112.199, "end": 112.259}, {"text": "sladké.", "start": 112.259, "end": 112.839}, {"text": "\n", "start": 112.839, "end": 113.419}, {"text": "\n", "start": 113.419, "end": 113.439}, {"text": "\"Presne", "start": 113.439, "end": 113.879}, {"text": " ", "start": 113.879, "end": 113.939}, {"text": "ako", "start": 113.939, "end": 114.139}, {"text": " ", "start": 114.139, "end": 114.199}, {"text": "od", "start": 114.199, "end": 114.319}, {"text": " ", "start": 114.319, "end": 114.36}, {"text": "<PERSON><PERSON><PERSON><PERSON>,\"", "start": 114.36, "end": 115.119}, {"text": " ", "start": 115.119, "end": 115.22}, {"text": "povie", "start": 115.22, "end": 115.499}, {"text": " ", "start": 115.499, "end": 115.559}, {"text": "<PERSON><PERSON>", "start": 115.559, "end": 116.019}, {"text": " ", "start": 116.019, "end": 116.04}, {"text": "spokojne.", "start": 116.04, "end": 116.959}, {"text": " ", "start": 116.959, "end": 117.019}, {"text": "\"Na", "start": 117.019, "end": 117.139}, {"text": " ", "start": 117.139, "end": 117.199}, {"text": "jeseň", "start": 117.199, "end": 117.519}, {"text": " ", "start": 117.519, "end": 117.579}, {"text": "sk<PERSON><PERSON>", "start": 117.579, "end": 118.059}, {"text": " ", "start": 118.059, "end": 118.119}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 118.119, "end": 118.679}, {"text": " ", "start": 118.679, "end": 118.739}, {"text": "so", "start": 118.739, "end": 118.879}, {"text": " ", "start": 118.879, "end": 118.899}, {"text": "sliv<PERSON><PERSON>", "start": 118.899, "end": 119.699}, {"text": " ", "start": 119.699, "end": 119.819}, {"text": "a", "start": 119.819, "end": 119.839}, {"text": " ", "start": 119.839, "end": 119.939}, {"text": "v", "start": 119.939, "end": 119.959}, {"text": " ", "start": 119.959, "end": 120.079}, {"text": "zime", "start": 120.079, "end": 120.299}, {"text": " ", "start": 120.299, "end": 120.459}, {"text": "s", "start": 120.459, "end": 120.479}, {"text": " ", "start": 120.479, "end": 120.5}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 120.5, "end": 121.079}, {"text": " ", "start": 121.079, "end": 121.139}, {"text": "malinami.\"", "start": 121.139, "end": 121.779}, {"text": "\n", "start": 121.779, "end": 122.559}, {"text": "\n", "start": 122.559, "end": 122.579}, {"text": "<PERSON><PERSON>", "start": 122.579, "end": 122.819}, {"text": " ", "start": 122.819, "end": 122.839}, {"text": "je", "start": 122.839, "end": 122.939}, {"text": " ", "start": 122.939, "end": 123.019}, {"text": "šťastná.", "start": 123.019, "end": 123.86}, {"text": " ", "start": 123.86, "end": 123.959}, {"text": "Dnes", "start": 123.959, "end": 124.179}, {"text": " ", "start": 124.179, "end": 124.199}, {"text": "sa", "start": 124.199, "end": 124.259}, {"text": " ", "start": 124.259, "end": 124.319}, {"text": "naučila", "start": 124.319, "end": 124.719}, {"text": " ", "start": 124.719, "end": 124.759}, {"text": "pripraviť", "start": 124.759, "end": 125.319}, {"text": " ", "start": 125.319, "end": 125.399}, {"text": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 125.399, "end": 125.939}, {"text": " ", "start": 125.939, "end": 126.0}, {"text": "slovenský", "start": 126.0, "end": 126.519}, {"text": " ", "start": 126.519, "end": 126.579}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 126.579, "end": 127.339}, {"text": " ", "start": 127.339, "end": 127.36}, {"text": "–", "start": 127.36, "end": 127.361}, {"text": " ", "start": 127.361, "end": 127.439}, {"text": "<PERSON><PERSON><PERSON><PERSON>,", "start": 127.439, "end": 128.139}, {"text": " ", "start": 128.139, "end": 128.22}, {"text": "ktorá", "start": 128.22, "end": 128.559}, {"text": " ", "start": 128.559, "end": 128.679}, {"text": "nikdy", "start": 128.679, "end": 129.039}, {"text": " ", "start": 129.039, "end": 129.08}, {"text": "nesklame.", "start": 129.08, "end": 129.739}]}