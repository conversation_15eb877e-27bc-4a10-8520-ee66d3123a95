'use client';

import { useState } from 'react';
import { Header } from '@/components/header/Header';
import TranslationModal from '@/components/header/TranslationModal';

export function ClientLayout({ children }: { children: React.ReactNode }) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const showImportStoryButton = true;

  return (
    <div className="min-h-screen bg-background">
      <Header
        onImportStoryClick={
          showImportStoryButton ? () => setIsModalOpen(true) : undefined
        }
      />
      {children}
      {showImportStoryButton && (
        <TranslationModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} />
      )}
    </div>
  );
}
