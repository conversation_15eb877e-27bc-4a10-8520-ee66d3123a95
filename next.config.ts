import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  reactStrictMode: true,
  images: {
    remotePatterns: [],
  },
  async headers() {
    return [
      {
        source: '/images/texture.webp',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=15768000, immutable', // 6 months,
          },
        ],
      },
      {
        source: '/ingest/static/(web-vitals.js|dead-clicks-autocapture.js|recorder.js)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=15768000, immutable', // 6 months,
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
        ],
      },
    ];
  },
  async rewrites() {
    return [
      {
        source: '/ingest/static/:path*',
        destination: 'https://eu-assets.i.posthog.com/static/:path*',
      },
      {
        source: '/ingest/:path*',
        destination: 'https://eu.i.posthog.com/:path*',
      },
      {
        source: '/ingest/decide',
        destination: 'https://eu.i.posthog.com/decide',
      },
    ];
  },
  // This is required to support PostHog trailing slash API requests
  //skipTrailingSlashRedirect: true,
};

export default nextConfig;
