'use client';

import { useState } from 'react';
import { LayoutWrapper } from '@/components/LayoutWrapper';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  BookOpenIcon, 
  ClockIcon, 
  UsersIcon,
  CalendarIcon,
  ArrowLeftIcon,
  GraduationCapIcon
} from 'lucide-react';
import { historicalStories, HistoricalStory } from '@/lib/mocks/historia/historical-stories';

const difficultyColors = {
  beginner: 'bg-green-500',
  intermediate: 'bg-yellow-500',
  advanced: 'bg-red-500'
};

const difficultyLabels = {
  beginner: 'Začiatočník',
  intermediate: 'Pokročilý',
  advanced: 'Expert'
};

export default function HistoricalStoriesPage() {
  const [selectedStory, setSelectedStory] = useState<HistoricalStory | null>(null);
  const [showEnglish, setShowEnglish] = useState(false);

  if (selectedStory) {
    return (
      <LayoutWrapper>
        <div className="max-w-4xl mx-auto pb-8">
          <div className="mb-6">
            <Button 
              variant="ghost" 
              onClick={() => setSelectedStory(null)}
              className="mb-4"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Späť na zoznam príbehov
            </Button>
            
            <div className="flex items-center gap-4 mb-4">
              <h1 className="text-3xl font-bold">{selectedStory.title}</h1>
              <Badge className={`${difficultyColors[selectedStory.difficulty]} text-white`}>
                {difficultyLabels[selectedStory.difficulty]}
              </Badge>
            </div>
            
            <p className="text-muted-foreground italic mb-2">{selectedStory.englishTitle}</p>
            
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <CalendarIcon className="h-4 w-4" />
                {selectedStory.period}
              </div>
              <div className="flex items-center gap-1">
                <ClockIcon className="h-4 w-4" />
                {selectedStory.estimatedReadTime} min čítania
              </div>
            </div>
          </div>

          <div className="grid gap-6">
            {/* Story Content */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <BookOpenIcon className="h-5 w-5" />
                    Príbeh
                  </CardTitle>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setShowEnglish(!showEnglish)}
                  >
                    {showEnglish ? 'Slovensky' : 'English'}
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[400px] pr-4">
                  <div className="prose prose-sm max-w-none">
                    {showEnglish ? (
                      <div className="whitespace-pre-line text-sm leading-relaxed">
                        {selectedStory.englishContent}
                      </div>
                    ) : (
                      <div className="whitespace-pre-line text-sm leading-relaxed">
                        {selectedStory.content}
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>

            {/* Key Figures and Events */}
            <div className="grid md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <UsersIcon className="h-5 w-5" />
                    Kľúčové osobnosti
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {selectedStory.keyFigures.map((figure, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                        <span className="text-sm">{figure}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CalendarIcon className="h-5 w-5" />
                    Kľúčové udalosti
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {selectedStory.keyEvents.map((event, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                        <span className="text-sm">{event}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </div>

            {/* Vocabulary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <GraduationCapIcon className="h-5 w-5" />
                  Slovník
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-3">
                  {selectedStory.vocabulary.map((item, index) => (
                    <div key={index} className="border-l-4 border-primary pl-4">
                      <div className="font-medium text-primary">{item.word}</div>
                      <div className="text-sm">{item.definition}</div>
                      <div className="text-xs text-muted-foreground italic">
                        {item.englishDefinition}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </LayoutWrapper>
    );
  }

  return (
    <LayoutWrapper>
      <div className="max-w-7xl mx-auto pb-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Historické príbehy</h1>
          <p className="text-muted-foreground">
            Spoznajte slovenskú históriu prostredníctvom zaujímavých príbehov
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {historicalStories.map((story) => (
            <Card 
              key={story.id} 
              className="cursor-pointer transition-all duration-200 hover:shadow-lg group"
              onClick={() => setSelectedStory(story)}
            >
              <CardHeader>
                <div className="flex items-center justify-between mb-2">
                  <Badge variant="outline">{story.period}</Badge>
                  <Badge className={`${difficultyColors[story.difficulty]} text-white`}>
                    {difficultyLabels[story.difficulty]}
                  </Badge>
                </div>
                <CardTitle className="text-lg group-hover:text-primary transition-colors">
                  {story.title}
                </CardTitle>
                <p className="text-sm text-muted-foreground italic">
                  {story.englishTitle}
                </p>
              </CardHeader>
              <CardContent>
                <p className="text-sm mb-4 line-clamp-3">{story.description}</p>
                
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <ClockIcon className="h-3 w-3" />
                    {story.estimatedReadTime} min
                  </div>
                  <div className="flex items-center gap-1">
                    <GraduationCapIcon className="h-3 w-3" />
                    {story.vocabulary.length} slov
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </LayoutWrapper>
  );
}
