import React from 'react';
import { SunIcon, MoonIcon } from 'lucide-react';
import { SWITCH_ON_AUDIO_URL, SWITCH_OFF_AUDIO_URL } from '@/lib/constants';
import { Button } from '@/components/ui/button';
import { useSoundEffect } from '@/lib/hooks/useSoundEffect';
import { useTheme } from '@/app/providers/ThemeProvider';

export const ThemeToggle: React.FC = () => {
  const { theme, setTheme } = useTheme();

  const playSwitchOn = useSoundEffect({
    audioUrl: SWITCH_ON_AUDIO_URL,
    volume: 0.3,
  });
  const playSwitchOff = useSoundEffect({
    audioUrl: SWITCH_OFF_AUDIO_URL,
    volume: 0.3,
  });

  const handleThemeChange = () => {
    const newDarkMode = theme === 'dark' ? false : true;
    const themeToApply = newDarkMode ? 'dark' : 'light';
    setTheme(themeToApply);
    if (newDarkMode) {
      playSwitchOn();
    } else {
      playSwitchOff();
    }
  };

  return (
    <Button
      variant="outline"
      size="default"
      onClick={handleThemeChange}
      title={theme === 'dark' ? 'Light Mode' : 'Dark Mode'}
      className="md:w-[140px]"
    >
      <span className="hidden md:flex items-center gap-2">
        <div className="relative w-4 h-4">
          <SunIcon
            size={16}
            className={`absolute inset-0 text-yellow-500 transition-all duration-300 ${theme === 'dark' ? 'opacity-100 scale-100 rotate-0' : 'opacity-0 scale-50 rotate-90'}`}
          />
          <MoonIcon
            size={16}
            className={`absolute inset-0 text-black/80 dark:text-slate-300 transition-all duration-300 ${theme === 'dark' ? 'opacity-0 scale-50 rotate-90' : 'opacity-100 scale-100 rotate-0'}`}
          />
        </div>
        <span className="transition-all duration-300">
          {theme === 'dark' ? 'Light Mode' : 'Dark Mode'}
        </span>
      </span>
      <span className="md:hidden relative w-4 h-4">
        <SunIcon
          size={16}
          className={`absolute inset-0 text-yellow-500 transition-all duration-300 ${theme === 'dark' ? 'opacity-100 scale-100 rotate-0' : 'opacity-0 scale-50 rotate-90'}`}
        />
        <MoonIcon
          size={16}
          className={`absolute inset-0 text-black/80 dark:text-slate-300 transition-all duration-300 ${theme === 'dark' ? 'opacity-0 scale-50 rotate-90' : 'opacity-100 scale-100 rotate-0'}`}
        />
      </span>
    </Button>
  );
};

export default ThemeToggle;
