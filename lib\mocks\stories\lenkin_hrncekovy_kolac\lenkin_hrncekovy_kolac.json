{"id": "lenkin-hrn<PERSON>kovy-kolac", "title": "<PERSON><PERSON> hrnčekový koláč", "english_title": "Lenka's <PERSON><PERSON>", "audio_url": "https://x9xfaca7mb.ufs.sh/f/TuGkI0HClmz3KzuuoB4luGdO9azhIn8gVrDHxPjmSWksJ0Bo", "image_url": "/images/stories/lenkin_hrn<PERSON><PERSON><PERSON>_kolac.png", "voice_id": "RILOU7YmBhvwJGDGjNmP", "description": "Mária a jej dcéra Lenka spolu pečú jednoduchý hrnčekový koláč.", "dictionary": [], "text": "<PERSON>kin hrnčekový koláč\n\nSobotné ráno v dome Novákovcov nebolo nikdy tiché, najm<PERSON> keď bola Lenka hore. Slnečné lúče prenikali cez kuchynské okno a hrali sa na podlahe, keď Lenka vtrhla do kuchyne.\n\n„Ma<PERSON>, čo dnes budeme robiť?\" spýtala sa Lenka, vyskočila na stoličku a začala hojdať nohami.\n\nMama Mária sa usmiala. Už mala plán. „Dnes ťa naučím piecť môj najobľúbenejší slovenský koláč. Volá sa 'Koláč deň a noc'.\"\n\nLenka prestala hojdať nohami. „Prečo sa volá deň a noc?\" spýtala sa a naklonila hlavu na stranu ako malé vtáčatko.\n\n„Pretože má tmavú časť a svetlú časť,\" vys<PERSON><PERSON><PERSON> mama, kým vyberala z chladničky vajíčka. „Tmavá časť je ako noc – tmavá a čokoládová. Svetlá časť je ako deň – biela a krémová.\"\n\nLenka zatlieskal rukami. „To znie úžasne! Môžem pomáhať?\"\n\n„Samozrejme! Preto to robíme spolu.\" Mama začala dávať na stôl všetky potrebné veci: vajcia, cukor, múku, kakao, maslo a mlieko. Potom vzala svoj obľúbený modrý hrnček.\n\nLenka na hrnček hľadela so zmäteným výrazom. „Na čo potrebujeme hrnček?\"\n\n„Tento koláč sa volá hrnčekový koláč. Vieš prečo?\" spýtala sa mama s tajomným úsmevom.\n\nLenka pokrútila hlavou.\n\n„Pretože hrnček používame ako odmerku! Takto ma to naučila moja babička – tvoja prababička. Nepotrebujeme žiadne špeciálne odmerky.\"\n\n„Naozaj?\" oči sa Lenke rozšírili od údivu. „To je super trik!\"\n\nMama podala Lenke misku. „Začneme s tmavou časťou – nocou. Môžeš rozbiť tri vajíčka do tejto misky?\"\n\nLenka sa sústredila, akoby išlo o najdôležitejšiu vec na svete. Opatrne rozbila každé vajce, dávajúc si veľký pozor, aby sa do misky nedostali žiadne kúsky škrupiny.\n\n„Výborne! Teraz vyšľaháme vajíčka s cukrom.\" Mama jej ukázala, ako držať šľahač a spolu vyšľahali vajíčka, až kým neboli nadýchané a svetlé.\n\n„Teraz pridáme pol hrnčeka mlieka,\" povedala mama a podala Lenke hrnček.\n\nLenka naliala mlieko presne po polovicu hrnčeka a potom ho opatrne pridala do misky.\n\n„A teraz rozpustené maslo, jeden hrnček múky, tri lyžice kakaa a lyžičku prášku do pečiva,\" povedala mama, podávajúc Lenke každú prísadu.\n\nLenka pridala všetko do misky a potom sa spýtala: „Mami, naozaj nepotrebujeme špeciálne odmerky ako v televízii?\"\n\nMama sa zasmiala. „To je práve to najlepšie na hrnčekovom koláči. Je jednoduchý a vždy sa vydarí. Jeden hrnček múky je jeden hrnček múky, nech už je hrnček akýkoľvek. Samozrejme, vždy používame ten istý hrnček pre celý recept.\"\n\nSpolu miešali cesto, až kým nebolo hladké a tmavé od kakaa.\n\n„Teraz ho vylejeme do formy,\" povedala mama a pomohla Lenke preliať cesto do vymastenej formy.\n\n„A už ide do rúry?\" spýtala sa Lenka nadšene.\n\n„Presne tak. Pri 180 stupňoch približne 30 minút.\"\n\nKým sa koláč piekol a naplnil kuchyňu božskou vôňou čokolády, mama umyla hrnček a začala pripravovať ďalšie ingrediencie.\n\n„Čo robíme teraz?\" Lenka stála na špičkách, aby videla cez okienko na koláč v rúre.\n\n„Teraz pripravíme svetlú časť – deň,\" vysvetlila mama. „Potrebujeme tvaroh, smotanu a broskyne.\"\n\nKeď sa čokoládový koláč upiekol a vychladol, mama ukázala Lenke, ako pripraviť krémovú tvarohovú zmes a natrieť ju na koláč.\n\n„Teraz musí ísť do chladničky,\" povedala mama.\n\n„Na ako dlho?\" spýtala sa Lenka a povzdychla si.\n\n„Len na hodinu,\" ubezpečila ju mama.\n\nNeskôr Lenka pomáhala mame ukladať plátky broskýň na tvarohovú vrstvu. Potom zaliali broskyne žiarivým želé.\n\n„A znova musí do chladničky?\" hádala Lenka.\n\n„Áno, už vieš ako na to,\" usmiala sa mama. „Tentoraz potrebuje štyri hodiny, aby sa želé dobre stuhlo.\"\n\nKeď večer prišiel otec domov, koláč bol pripravený. Mama ho slávnostne priniesla na stôl – bol nádherný s tmavou čokoládovou vrstvou na spodku, bielou tvarohovou vrstvou v strede a zlatými broskyňami na vrchu.\n\n„Je úžasný!\" zvolal otec po prvom súste. „Lenka, ty si toto pomáhala robiť?\"\n\nLenka hrdo prikývla, líca mala od radosti červené. „Mami ma naučila, ako sa používa hrnček namiesto odmerky!\"\n\n„Moja malá cukrárka,\" zasmial sa otec.\n\n„Mami,\" povedala Lenka s plnými ústami koláča, „môžeme zajtra upiecť ďalší hrnčekový koláč?\"\n\nMama sa pozrela na otca a obaja sa usmiali. „Samozrejme. Poznám veľa receptov na hrnčekové koláče. Možno jahodový?\"\n\nLenkine oči sa rozžiarili. „Áno!\" zakričala nadšene. „Jahodový hrnčekový koláč!\"\n\nA tak sa začalo Lenkino dobrodružstvo so slovenskými hrnčekovými koláčmi. S každým receptom sa učila nové slovenské slová a nové cukrárske triky. A najlepšie na tom bolo, že všetko, čo potrebovala, bol jeden obyčajný hrnček a mamina pomoc.", "translation": "<PERSON><PERSON>'s <PERSON>g Cake\n\nSaturday morning in the <PERSON><PERSON> household was never quiet, especially when <PERSON><PERSON> was awake. Sunbeams streamed through the kitchen window and danced on the floor as <PERSON><PERSON> burst into the kitchen.\n\n\"Mom, what are we going to do today?\" asked <PERSON><PERSON>, jumping onto a chair and swinging her legs.\n\n<PERSON> smiled. She already had a plan. \"Today I'm going to teach you how to bake my favorite Slovak cake. It's called 'Day and Night Cake.'\"\n\n<PERSON><PERSON> stopped swinging her legs. \"Why is it called day and night?\" she asked, tilting her head to the side like a little bird.\n\n\"Because it has a dark part and a light part,\" explained <PERSON> while taking eggs from the refrigerator. \"The dark part is like night – dark and chocolatey. The light part is like day – white and creamy.\"\n\n<PERSON><PERSON> clapped her hands. \"That sounds amazing! Can I help?\"\n\n\"Of course! That's why we're doing this together.\" Mom began putting all the necessary items on the table: eggs, sugar, flour, cocoa, butter, and milk. Then she took her favorite blue mug.\n\n<PERSON><PERSON> looked at the mug with a confused expression. \"What do we need a mug for?\"\n\n\"This cake is called a mug cake. Do you know why?\" <PERSON> asked with a mysterious smile.\n\n<PERSON><PERSON> shook her head.\n\n\"Because we use the mug as a measuring cup! This is how my grandmother – your great-grandmother – taught me. We don't need any special measuring cups.\"\n\n\"Really?\" <PERSON><PERSON>'s eyes widened in surprise. \"That's a super trick!\"\n\n<PERSON> handed <PERSON><PERSON> a bowl. \"We'll start with the dark part – the night. Can you break three eggs into this bowl?\"\n\n<PERSON>ka concentrated as if it were the most important thing in the world. She carefully cracked each egg, taking great care not to get any pieces of shell in the bowl.\n\n\"Excellent! Now we'll beat the eggs with sugar.\" <PERSON> showed her how to hold the whisk, and together they beat the eggs until they were fluffy and light.\n\n\"Now we'll add half a mug of milk,\" said Mom, handing <PERSON>ka the mug.\n\n<PERSON><PERSON> poured milk exactly to the halfway mark of the mug and then carefully added it to the bowl.\n\n\"And now melted butter, one mug of flour, three tablespoons of cocoa, and a teaspoon of baking powder,\" said Mom, handing Lenka each ingredient.\n\nLenka added everything to the bowl and then asked, \"Mom, do we really not need special measuring cups like on TV?\"\n\nMom laughed. \"That's the best thing about mug cake. It's simple and always turns out well. One mug of flour is one mug of flour, no matter what mug it is. Of course, we always use the same mug for the entire recipe.\"\n\nTogether they mixed the batter until it was smooth and dark from the cocoa.\n\n\"Now we'll pour it into the pan,\" said Mom and helped Lenka transfer the batter into a greased pan.\n\n\"And now it goes into the oven?\" Lenka asked excitedly.\n\n\"Exactly. At 180 degrees for about 30 minutes.\"\n\nWhile the cake was baking and filling the kitchen with the divine aroma of chocolate, Mom washed the mug and began preparing additional ingredients.\n\n\"What are we doing now?\" Lenka stood on her tiptoes to see through the window at the cake in the oven.\n\n\"Now we'll prepare the light part – the day,\" explained Mom. \"We need quark cheese, cream, and peaches.\"\n\nWhen the chocolate cake was baked and cooled, Mom showed Lenka how to prepare a creamy quark mixture and spread it on the cake.\n\n\"Now it has to go into the refrigerator,\" said Mom.\n\n\"For how long?\" asked Lenka with a sigh.\n\n\"Just for an hour,\" Mom assured her.\n\nLater, Lenka helped Mom arrange slices of peaches on the quark layer. Then they poured shimmering jelly over the peaches.\n\n\"And it has to go back into the refrigerator?\" guessed Lenka.\n\n\"Yes, you're catching on,\" smiled Mom. \"This time it needs four hours for the jelly to set properly.\"\n\nWhen Dad came home in the evening, the cake was ready. Mom ceremoniously brought it to the table – it was beautiful with a dark chocolate layer on the bottom, a white quark layer in the middle, and golden peaches on top.\n\n\"It's amazing!\" exclaimed Dad after the first bite. \"Lenka, did you help make this?\"\n\nLenka nodded proudly, her cheeks red with joy. \"Mom taught me how to use a mug instead of a measuring cup!\"\n\n\"My little pastry chef,\" laughed Dad.\n\n\"Mom,\" said Lenka with her mouth full of cake, \"can we bake another mug cake tomorrow?\"\n\nMom looked at Dad, and they both smiled. \"Of course. I know many recipes for mug cakes. Maybe strawberry?\"\n\nLenka's eyes lit up. \"Yes!\" she shouted excitedly. \"Strawberry mug cake!\"\n\nAnd so began Lenka's adventure with Slovak mug cakes. With each recipe, she learned new Slovak words and new baking tricks. And the best part was that all she needed was one ordinary mug and Mom's help.", "words": [{"text": "<PERSON><PERSON>", "start": 0.099, "end": 0.459}, {"text": " ", "start": 0.459, "end": 0.539}, {"text": "hrnčekový", "start": 0.539, "end": 1.12}, {"text": " ", "start": 1.12, "end": 1.199}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 1.199, "end": 1.679}, {"text": "\n", "start": 1.679, "end": 4.079}, {"text": "\n", "start": 4.079, "end": 4.139}, {"text": "So<PERSON>né", "start": 4.139, "end": 4.619}, {"text": " ", "start": 4.619, "end": 4.639}, {"text": "r<PERSON>o", "start": 4.639, "end": 4.94}, {"text": " ", "start": 4.94, "end": 5.0}, {"text": "v", "start": 5.0, "end": 5.019}, {"text": " ", "start": 5.019, "end": 5.059}, {"text": "dome", "start": 5.059, "end": 5.259}, {"text": " ", "start": 5.259, "end": 5.299}, {"text": "Novákovcov", "start": 5.299, "end": 6.0}, {"text": " ", "start": 6.0, "end": 6.039}, {"text": "nebolo", "start": 6.039, "end": 6.379}, {"text": " ", "start": 6.379, "end": 6.42}, {"text": "nikdy", "start": 6.42, "end": 6.699}, {"text": " ", "start": 6.699, "end": 6.759}, {"text": "tich<PERSON>,", "start": 6.759, "end": 7.119}, {"text": " ", "start": 7.119, "end": 7.659}, {"text": "najmä", "start": 7.659, "end": 7.94}, {"text": " ", "start": 7.94, "end": 7.98}, {"text": "keď", "start": 7.98, "end": 8.119}, {"text": " ", "start": 8.119, "end": 8.179}, {"text": "bola", "start": 8.179, "end": 8.359}, {"text": " ", "start": 8.359, "end": 8.399}, {"text": "Len<PERSON>", "start": 8.399, "end": 8.699}, {"text": " ", "start": 8.699, "end": 8.76}, {"text": "hore.", "start": 8.76, "end": 9.139}, {"text": " ", "start": 9.139, "end": 10.039}, {"text": "Slnečné", "start": 10.039, "end": 10.599}, {"text": " ", "start": 10.599, "end": 10.659}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 10.659, "end": 11.0}, {"text": " ", "start": 11.0, "end": 11.059}, {"text": "<PERSON><PERSON><PERSON>", "start": 11.059, "end": 11.559}, {"text": " ", "start": 11.559, "end": 11.619}, {"text": "cez", "start": 11.619, "end": 11.759}, {"text": " ", "start": 11.759, "end": 11.8}, {"text": "kuchynské", "start": 11.8, "end": 12.279}, {"text": " ", "start": 12.279, "end": 12.399}, {"text": "okno", "start": 12.399, "end": 13.079}, {"text": " ", "start": 13.079, "end": 13.159}, {"text": "a", "start": 13.159, "end": 13.199}, {"text": " ", "start": 13.199, "end": 13.239}, {"text": "h<PERSON>i", "start": 13.239, "end": 13.519}, {"text": " ", "start": 13.519, "end": 13.559}, {"text": "sa", "start": 13.559, "end": 13.639}, {"text": " ", "start": 13.639, "end": 13.679}, {"text": "na", "start": 13.679, "end": 13.759}, {"text": " ", "start": 13.759, "end": 13.84}, {"text": "<PERSON><PERSON><PERSON>,", "start": 13.84, "end": 14.239}, {"text": " ", "start": 14.239, "end": 14.319}, {"text": "keď", "start": 14.319, "end": 14.46}, {"text": " ", "start": 14.46, "end": 14.479}, {"text": "Len<PERSON>", "start": 14.479, "end": 14.779}, {"text": " ", "start": 14.779, "end": 14.799}, {"text": "vtrhla", "start": 14.799, "end": 15.179}, {"text": " ", "start": 15.179, "end": 15.219}, {"text": "do", "start": 15.219, "end": 15.279}, {"text": " ", "start": 15.279, "end": 15.339}, {"text": "kuchyne.", "start": 15.339, "end": 15.839}, {"text": "\n", "start": 15.839, "end": 17.02}, {"text": "\n", "start": 17.02, "end": 17.04}, {"text": "<PERSON><PERSON><PERSON>,", "start": 17.04, "end": 17.319}, {"text": " ", "start": 17.319, "end": 17.44}, {"text": "čo", "start": 17.44, "end": 17.52}, {"text": " ", "start": 17.52, "end": 17.579}, {"text": "dnes", "start": 17.579, "end": 17.799}, {"text": " ", "start": 17.799, "end": 17.819}, {"text": "budeme", "start": 17.819, "end": 18.139}, {"text": " ", "start": 18.139, "end": 18.18}, {"text": "robiť?\"", "start": 18.18, "end": 18.559}, {"text": " ", "start": 18.559, "end": 19.279}, {"text": "s<PERSON><PERSON><PERSON><PERSON>", "start": 19.279, "end": 19.679}, {"text": " ", "start": 19.679, "end": 19.719}, {"text": "sa", "start": 19.719, "end": 19.799}, {"text": " ", "start": 19.799, "end": 19.859}, {"text": "Lenka,", "start": 19.859, "end": 20.159}, {"text": " ", "start": 20.159, "end": 20.659}, {"text": "vyskočila", "start": 20.659, "end": 21.199}, {"text": " ", "start": 21.199, "end": 21.239}, {"text": "na", "start": 21.239, "end": 21.339}, {"text": " ", "start": 21.339, "end": 21.399}, {"text": "sto<PERSON><PERSON><PERSON>", "start": 21.399, "end": 22.159}, {"text": " ", "start": 22.159, "end": 22.199}, {"text": "a", "start": 22.199, "end": 22.259}, {"text": " ", "start": 22.259, "end": 22.279}, {"text": "začala", "start": 22.279, "end": 22.659}, {"text": " ", "start": 22.659, "end": 22.719}, {"text": "hojdať", "start": 22.719, "end": 23.079}, {"text": " ", "start": 23.079, "end": 23.139}, {"text": "<PERSON><PERSON>i.", "start": 23.139, "end": 23.559}, {"text": "\n", "start": 23.559, "end": 24.76}, {"text": "\n", "start": 24.76, "end": 24.799}, {"text": "<PERSON>", "start": 24.799, "end": 25.019}, {"text": " ", "start": 25.019, "end": 25.18}, {"text": "<PERSON><PERSON><PERSON>", "start": 25.18, "end": 25.539}, {"text": " ", "start": 25.539, "end": 25.599}, {"text": "sa", "start": 25.599, "end": 25.699}, {"text": " ", "start": 25.699, "end": 25.76}, {"text": "usmiala.", "start": 25.76, "end": 26.34}, {"text": " ", "start": 26.34, "end": 26.799}, {"text": "Už", "start": 26.799, "end": 26.899}, {"text": " ", "start": 26.899, "end": 26.939}, {"text": "mala", "start": 26.939, "end": 27.139}, {"text": " ", "start": 27.139, "end": 27.18}, {"text": "plán.", "start": 27.18, "end": 27.5}, {"text": " ", "start": 27.5, "end": 28.459}, {"text": "„Dnes", "start": 28.459, "end": 28.84}, {"text": " ", "start": 28.84, "end": 28.899}, {"text": "<PERSON><PERSON>", "start": 28.899, "end": 28.979}, {"text": " ", "start": 28.979, "end": 29.019}, {"text": "naučím", "start": 29.019, "end": 29.459}, {"text": " ", "start": 29.459, "end": 29.519}, {"text": "piecť", "start": 29.519, "end": 29.939}, {"text": " ", "start": 29.939, "end": 29.979}, {"text": "m<PERSON><PERSON>", "start": 29.979, "end": 30.159}, {"text": " ", "start": 30.159, "end": 30.219}, {"text": "najobľú<PERSON><PERSON>", "start": 30.219, "end": 31.139}, {"text": " ", "start": 31.139, "end": 31.199}, {"text": "slovenský", "start": 31.199, "end": 31.699}, {"text": " ", "start": 31.699, "end": 31.76}, {"text": "kol<PERSON>č.", "start": 31.76, "end": 32.259}, {"text": " ", "start": 32.259, "end": 32.84}, {"text": "Volá", "start": 32.84, "end": 33.119}, {"text": " ", "start": 33.119, "end": 33.18}, {"text": "sa", "start": 33.18, "end": 33.699}, {"text": " ", "start": 33.699, "end": 33.739}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 33.739, "end": 34.34}, {"text": " ", "start": 34.34, "end": 34.52}, {"text": "deň", "start": 34.52, "end": 34.839}, {"text": " ", "start": 34.839, "end": 34.88}, {"text": "a", "start": 34.88, "end": 34.979}, {"text": " ", "start": 34.979, "end": 35.04}, {"text": "noc'.\"", "start": 35.04, "end": 35.36}, {"text": "\n", "start": 35.36, "end": 36.5}, {"text": "\n", "start": 36.5, "end": 36.559}, {"text": "Len<PERSON>", "start": 36.559, "end": 36.88}, {"text": " ", "start": 36.88, "end": 36.919}, {"text": "prestala", "start": 36.919, "end": 37.34}, {"text": " ", "start": 37.34, "end": 37.38}, {"text": "hojdať", "start": 37.38, "end": 37.719}, {"text": " ", "start": 37.719, "end": 37.759}, {"text": "<PERSON><PERSON>i.", "start": 37.759, "end": 38.259}, {"text": " ", "start": 38.259, "end": 38.779}, {"text": "„Prečo", "start": 38.779, "end": 39.079}, {"text": " ", "start": 39.079, "end": 39.119}, {"text": "sa", "start": 39.119, "end": 39.219}, {"text": " ", "start": 39.219, "end": 39.259}, {"text": "volá", "start": 39.259, "end": 39.5}, {"text": " ", "start": 39.5, "end": 39.52}, {"text": "deň", "start": 39.52, "end": 39.719}, {"text": " ", "start": 39.719, "end": 39.759}, {"text": "a", "start": 39.759, "end": 39.819}, {"text": " ", "start": 39.819, "end": 39.879}, {"text": "noc?\"", "start": 39.879, "end": 40.219}, {"text": " ", "start": 40.219, "end": 40.819}, {"text": "s<PERSON><PERSON><PERSON><PERSON>", "start": 40.819, "end": 41.319}, {"text": " ", "start": 41.319, "end": 41.379}, {"text": "sa", "start": 41.379, "end": 41.719}, {"text": " ", "start": 41.719, "end": 41.779}, {"text": "a", "start": 41.779, "end": 41.819}, {"text": " ", "start": 41.819, "end": 41.879}, {"text": "naklonila", "start": 41.879, "end": 42.379}, {"text": " ", "start": 42.379, "end": 42.419}, {"text": "hlavu", "start": 42.419, "end": 42.68}, {"text": " ", "start": 42.68, "end": 42.739}, {"text": "na", "start": 42.739, "end": 42.84}, {"text": " ", "start": 42.84, "end": 42.879}, {"text": "stranu", "start": 42.879, "end": 43.259}, {"text": " ", "start": 43.259, "end": 43.319}, {"text": "ako", "start": 43.319, "end": 43.479}, {"text": " ", "start": 43.479, "end": 43.54}, {"text": "malé", "start": 43.54, "end": 43.739}, {"text": " ", "start": 43.739, "end": 43.779}, {"text": "vtáčatko.", "start": 43.779, "end": 44.5}, {"text": "\n", "start": 44.5, "end": 45.399}, {"text": "\n", "start": 45.399, "end": 45.419}, {"text": "„Pretože", "start": 45.419, "end": 45.779}, {"text": " ", "start": 45.779, "end": 45.84}, {"text": "má", "start": 45.84, "end": 46.019}, {"text": " ", "start": 46.019, "end": 46.059}, {"text": "tmavú", "start": 46.059, "end": 46.439}, {"text": " ", "start": 46.439, "end": 46.52}, {"text": "časť", "start": 46.52, "end": 47.059}, {"text": " ", "start": 47.059, "end": 47.119}, {"text": "a", "start": 47.119, "end": 47.18}, {"text": " ", "start": 47.18, "end": 47.239}, {"text": "svetlú", "start": 47.239, "end": 47.579}, {"text": " ", "start": 47.579, "end": 47.659}, {"text": "časť,\"", "start": 47.659, "end": 48.0}, {"text": " ", "start": 48.0, "end": 48.799}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 48.799, "end": 49.299}, {"text": " ", "start": 49.299, "end": 49.34}, {"text": "mama,", "start": 49.34, "end": 49.639}, {"text": " ", "start": 49.639, "end": 49.979}, {"text": "kým", "start": 49.979, "end": 50.139}, {"text": " ", "start": 50.139, "end": 50.159}, {"text": "vyberala", "start": 50.159, "end": 50.579}, {"text": " ", "start": 50.579, "end": 50.599}, {"text": "z", "start": 50.599, "end": 50.639}, {"text": " ", "start": 50.639, "end": 50.659}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 50.659, "end": 51.159}, {"text": " ", "start": 51.159, "end": 51.199}, {"text": "vajíčka.", "start": 51.199, "end": 51.659}, {"text": " ", "start": 51.659, "end": 52.52}, {"text": "„Tmavá", "start": 52.52, "end": 52.879}, {"text": " ", "start": 52.879, "end": 52.939}, {"text": "časť", "start": 52.939, "end": 53.239}, {"text": " ", "start": 53.239, "end": 53.259}, {"text": "je", "start": 53.259, "end": 53.34}, {"text": " ", "start": 53.34, "end": 53.399}, {"text": "ako", "start": 53.399, "end": 53.579}, {"text": " ", "start": 53.579, "end": 53.639}, {"text": "noc", "start": 53.639, "end": 54.199}, {"text": " ", "start": 54.199, "end": 54.239}, {"text": "–", "start": 54.239, "end": 54.239999999999995}, {"text": " ", "start": 54.239999999999995, "end": 54.299}, {"text": "tmavá", "start": 54.299, "end": 54.959}, {"text": " ", "start": 54.959, "end": 55.02}, {"text": "a", "start": 55.02, "end": 55.059}, {"text": " ", "start": 55.059, "end": 55.139}, {"text": "čokoládová.", "start": 55.139, "end": 55.819}, {"text": " ", "start": 55.819, "end": 56.5}, {"text": "Svetlá", "start": 56.5, "end": 56.819}, {"text": " ", "start": 56.819, "end": 56.899}, {"text": "časť", "start": 56.899, "end": 57.199}, {"text": " ", "start": 57.199, "end": 57.219}, {"text": "je", "start": 57.219, "end": 57.279}, {"text": " ", "start": 57.279, "end": 57.36}, {"text": "ako", "start": 57.36, "end": 57.52}, {"text": " ", "start": 57.52, "end": 57.559}, {"text": "deň", "start": 57.559, "end": 58.139}, {"text": " ", "start": 58.139, "end": 58.159}, {"text": "–", "start": 58.159, "end": 58.16}, {"text": " ", "start": 58.16, "end": 58.199}, {"text": "biela", "start": 58.199, "end": 58.819}, {"text": " ", "start": 58.819, "end": 58.859}, {"text": "a", "start": 58.859, "end": 58.919}, {"text": " ", "start": 58.919, "end": 58.959}, {"text": "krémová.\"", "start": 58.959, "end": 59.559}, {"text": "\n", "start": 59.559, "end": 60.639}, {"text": "\n", "start": 60.639, "end": 60.659}, {"text": "Len<PERSON>", "start": 60.659, "end": 60.959}, {"text": " ", "start": 60.959, "end": 61.02}, {"text": "zatlieskal", "start": 61.02, "end": 61.639}, {"text": " ", "start": 61.639, "end": 61.699}, {"text": "ruka<PERSON>.", "start": 61.699, "end": 62.219}, {"text": " ", "start": 62.219, "end": 62.979}, {"text": "„To", "start": 62.979, "end": 63.139}, {"text": " ", "start": 63.139, "end": 63.199}, {"text": "znie", "start": 63.199, "end": 63.439}, {"text": " ", "start": 63.439, "end": 63.539}, {"text": "úžasne!", "start": 63.539, "end": 64.139}, {"text": " ", "start": 64.139, "end": 64.459}, {"text": "M<PERSON>ž<PERSON>", "start": 64.459, "end": 64.799}, {"text": " ", "start": 64.799, "end": 64.839}, {"text": "pomáhať?\"", "start": 64.839, "end": 65.439}, {"text": "\n", "start": 65.439, "end": 66.459}, {"text": "\n", "start": 66.459, "end": 66.46000000000001}, {"text": "„Samozrejme!", "start": 66.46000000000001, "end": 67.239}, {"text": " ", "start": 67.239, "end": 67.659}, {"text": "Preto", "start": 67.659, "end": 67.879}, {"text": " ", "start": 67.879, "end": 67.939}, {"text": "to", "start": 67.939, "end": 67.979}, {"text": " ", "start": 67.979, "end": 68.019}, {"text": "r<PERSON><PERSON><PERSON>", "start": 68.019, "end": 68.319}, {"text": " ", "start": 68.319, "end": 68.379}, {"text": "spolu.\"", "start": 68.379, "end": 68.799}, {"text": " ", "start": 68.799, "end": 69.839}, {"text": "<PERSON>", "start": 69.839, "end": 70.059}, {"text": " ", "start": 70.059, "end": 70.119}, {"text": "začala", "start": 70.119, "end": 70.459}, {"text": " ", "start": 70.459, "end": 70.519}, {"text": "dávať", "start": 70.519, "end": 70.859}, {"text": " ", "start": 70.859, "end": 70.919}, {"text": "na", "start": 70.919, "end": 71.019}, {"text": " ", "start": 71.019, "end": 71.059}, {"text": "stôl", "start": 71.059, "end": 71.339}, {"text": " ", "start": 71.339, "end": 71.4}, {"text": "všetky", "start": 71.4, "end": 71.739}, {"text": " ", "start": 71.739, "end": 71.779}, {"text": "potrebné", "start": 71.779, "end": 72.199}, {"text": " ", "start": 72.199, "end": 72.239}, {"text": "veci:", "start": 72.239, "end": 72.659}, {"text": " ", "start": 72.659, "end": 73.139}, {"text": "vajcia,", "start": 73.139, "end": 73.68}, {"text": " ", "start": 73.68, "end": 73.919}, {"text": "cu<PERSON>,", "start": 73.919, "end": 74.639}, {"text": " ", "start": 74.639, "end": 74.72}, {"text": "<PERSON><PERSON><PERSON>,", "start": 74.72, "end": 75.279}, {"text": " ", "start": 75.279, "end": 75.36}, {"text": "kakao,", "start": 75.36, "end": 76.019}, {"text": " ", "start": 76.019, "end": 76.119}, {"text": "maslo", "start": 76.119, "end": 76.699}, {"text": " ", "start": 76.699, "end": 76.759}, {"text": "a", "start": 76.759, "end": 76.799}, {"text": " ", "start": 76.799, "end": 76.86}, {"text": "mlieko.", "start": 76.86, "end": 77.379}, {"text": " ", "start": 77.379, "end": 78.08}, {"text": "<PERSON><PERSON>", "start": 78.08, "end": 78.36}, {"text": " ", "start": 78.36, "end": 78.4}, {"text": "vzala", "start": 78.4, "end": 78.72}, {"text": " ", "start": 78.72, "end": 78.779}, {"text": "svoj", "start": 78.779, "end": 78.959}, {"text": " ", "start": 78.959, "end": 79.04}, {"text": "ob<PERSON><PERSON><PERSON><PERSON>", "start": 79.04, "end": 79.519}, {"text": " ", "start": 79.519, "end": 79.559}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 79.559, "end": 79.859}, {"text": " ", "start": 79.859, "end": 79.879}, {"text": "<PERSON><PERSON><PERSON><PERSON>.", "start": 79.879, "end": 80.5}, {"text": "\n", "start": 80.5, "end": 80.959}, {"text": "\n", "start": 80.959, "end": 81.04}, {"text": "Len<PERSON>", "start": 81.04, "end": 81.339}, {"text": " ", "start": 81.339, "end": 81.4}, {"text": "na", "start": 81.4, "end": 81.479}, {"text": " ", "start": 81.479, "end": 81.519}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 81.519, "end": 81.959}, {"text": " ", "start": 81.959, "end": 82.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 82.0, "end": 82.4}, {"text": " ", "start": 82.4, "end": 82.459}, {"text": "so", "start": 82.459, "end": 82.539}, {"text": " ", "start": 82.539, "end": 82.58}, {"text": "zmäteným", "start": 82.58, "end": 83.019}, {"text": " ", "start": 83.019, "end": 83.059}, {"text": "výrazom.", "start": 83.059, "end": 83.72}, {"text": " ", "start": 83.72, "end": 84.319}, {"text": "„Na", "start": 84.319, "end": 84.479}, {"text": " ", "start": 84.479, "end": 84.54}, {"text": "čo", "start": 84.54, "end": 84.619}, {"text": " ", "start": 84.619, "end": 84.659}, {"text": "potrebujeme", "start": 84.659, "end": 85.199}, {"text": " ", "start": 85.199, "end": 85.239}, {"text": "<PERSON><PERSON><PERSON><PERSON>?\"", "start": 85.239, "end": 85.9}, {"text": "\n", "start": 85.9, "end": 87.079}, {"text": "\n", "start": 87.079, "end": 87.08}, {"text": "„Tento", "start": 87.08, "end": 87.379}, {"text": " ", "start": 87.379, "end": 87.439}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 87.439, "end": 87.799}, {"text": " ", "start": 87.799, "end": 87.86}, {"text": "sa", "start": 87.86, "end": 87.939}, {"text": " ", "start": 87.939, "end": 88.0}, {"text": "volá", "start": 88.0, "end": 88.199}, {"text": " ", "start": 88.199, "end": 88.239}, {"text": "hrnčekový", "start": 88.239, "end": 88.859}, {"text": " ", "start": 88.859, "end": 88.919}, {"text": "kol<PERSON>č.", "start": 88.919, "end": 89.36}, {"text": " ", "start": 89.36, "end": 89.839}, {"text": "<PERSON><PERSON><PERSON>", "start": 89.839, "end": 90.04}, {"text": " ", "start": 90.04, "end": 90.079}, {"text": "prečo?\"", "start": 90.079, "end": 90.54}, {"text": " ", "start": 90.54, "end": 91.279}, {"text": "s<PERSON><PERSON><PERSON><PERSON>", "start": 91.279, "end": 91.679}, {"text": " ", "start": 91.679, "end": 91.72}, {"text": "sa", "start": 91.72, "end": 91.819}, {"text": " ", "start": 91.819, "end": 91.879}, {"text": "mama", "start": 91.879, "end": 92.139}, {"text": " ", "start": 92.139, "end": 92.199}, {"text": "s", "start": 92.199, "end": 92.219}, {"text": " ", "start": 92.219, "end": 92.279}, {"text": "tajomným", "start": 92.279, "end": 92.739}, {"text": " ", "start": 92.739, "end": 92.86}, {"text": "úsmevom.", "start": 92.86, "end": 94.219}, {"text": "\n", "start": 94.219, "end": 94.259}, {"text": "\n", "start": 94.259, "end": 94.319}, {"text": "Len<PERSON>", "start": 94.319, "end": 94.619}, {"text": " ", "start": 94.619, "end": 94.68}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 94.68, "end": 95.199}, {"text": " ", "start": 95.199, "end": 95.259}, {"text": "h<PERSON><PERSON>.", "start": 95.259, "end": 95.759}, {"text": "\n", "start": 95.759, "end": 96.419}, {"text": "\n", "start": 96.419, "end": 96.42}, {"text": "„Pretože", "start": 96.42, "end": 96.799}, {"text": " ", "start": 96.799, "end": 96.86}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 96.86, "end": 97.299}, {"text": " ", "start": 97.299, "end": 97.36}, {"text": "pou<PERSON><PERSON><PERSON><PERSON>", "start": 97.36, "end": 97.939}, {"text": " ", "start": 97.939, "end": 98.0}, {"text": "ako", "start": 98.0, "end": 98.199}, {"text": " ", "start": 98.199, "end": 98.279}, {"text": "odmer<PERSON>!", "start": 98.279, "end": 99.279}, {"text": " ", "start": 99.279, "end": 99.339}, {"text": "Takto", "start": 99.339, "end": 99.599}, {"text": " ", "start": 99.599, "end": 99.639}, {"text": "ma", "start": 99.639, "end": 99.739}, {"text": " ", "start": 99.739, "end": 99.779}, {"text": "to", "start": 99.779, "end": 99.86}, {"text": " ", "start": 99.86, "end": 99.9}, {"text": "naučila", "start": 99.9, "end": 100.319}, {"text": " ", "start": 100.319, "end": 100.379}, {"text": "moja", "start": 100.379, "end": 100.54}, {"text": " ", "start": 100.54, "end": 100.599}, {"text": "babička", "start": 100.599, "end": 101.259}, {"text": " ", "start": 101.259, "end": 101.299}, {"text": "–", "start": 101.299, "end": 101.30000000000001}, {"text": " ", "start": 101.30000000000001, "end": 101.339}, {"text": "tvoja", "start": 101.339, "end": 101.639}, {"text": " ", "start": 101.639, "end": 101.699}, {"text": "prababička.", "start": 101.699, "end": 102.419}, {"text": " ", "start": 102.419, "end": 102.919}, {"text": "Nepotrebujeme", "start": 102.919, "end": 103.619}, {"text": " ", "start": 103.619, "end": 103.68}, {"text": "žiadne", "start": 103.68, "end": 104.059}, {"text": " ", "start": 104.059, "end": 104.22}, {"text": "špeciálne", "start": 104.22, "end": 104.739}, {"text": " ", "start": 104.739, "end": 104.799}, {"text": "od<PERSON><PERSON>.\"", "start": 104.799, "end": 107.159}, {"text": "\n", "start": 107.159, "end": 107.22}, {"text": "\n", "start": 107.22, "end": 107.239}, {"text": "„<PERSON><PERSON><PERSON>?\"", "start": 107.239, "end": 108.419}, {"text": " ", "start": 108.419, "end": 108.639}, {"text": "<PERSON><PERSON><PERSON>", "start": 108.639, "end": 108.759}, {"text": " ", "start": 108.759, "end": 108.819}, {"text": "sa", "start": 108.819, "end": 108.919}, {"text": " ", "start": 108.919, "end": 108.959}, {"text": "<PERSON><PERSON>", "start": 108.959, "end": 109.279}, {"text": " ", "start": 109.279, "end": 109.319}, {"text": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 109.319, "end": 109.939}, {"text": " ", "start": 109.939, "end": 110.019}, {"text": "od", "start": 110.019, "end": 110.099}, {"text": " ", "start": 110.099, "end": 110.379}, {"text": "údivu.", "start": 110.379, "end": 111.419}, {"text": " ", "start": 111.419, "end": 111.479}, {"text": "„To", "start": 111.479, "end": 111.579}, {"text": " ", "start": 111.579, "end": 111.639}, {"text": "je", "start": 111.639, "end": 111.799}, {"text": " ", "start": 111.799, "end": 111.819}, {"text": "super", "start": 111.819, "end": 112.139}, {"text": " ", "start": 112.139, "end": 112.199}, {"text": "trik!\"", "start": 112.199, "end": 113.899}, {"text": "\n", "start": 113.899, "end": 113.959}, {"text": "\n", "start": 113.959, "end": 113.979}, {"text": "<PERSON>", "start": 113.979, "end": 114.099}, {"text": " ", "start": 114.099, "end": 114.199}, {"text": "podala", "start": 114.199, "end": 114.5}, {"text": " ", "start": 114.5, "end": 114.559}, {"text": "<PERSON><PERSON>", "start": 114.559, "end": 114.859}, {"text": " ", "start": 114.859, "end": 114.919}, {"text": "misku.", "start": 114.919, "end": 115.899}, {"text": " ", "start": 115.899, "end": 115.979}, {"text": "„Začneme", "start": 115.979, "end": 116.439}, {"text": " ", "start": 116.439, "end": 116.5}, {"text": "s", "start": 116.5, "end": 116.54}, {"text": " ", "start": 116.54, "end": 116.599}, {"text": "tmavou", "start": 116.599, "end": 117.279}, {"text": " ", "start": 117.279, "end": 117.299}, {"text": "čas<PERSON><PERSON>", "start": 117.299, "end": 117.839}, {"text": " ", "start": 117.839, "end": 117.86}, {"text": "–", "start": 117.86, "end": 117.861}, {"text": " ", "start": 117.861, "end": 117.899}, {"text": "nocou.", "start": 117.899, "end": 119.039}, {"text": " ", "start": 119.039, "end": 119.119}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 119.119, "end": 119.479}, {"text": " ", "start": 119.479, "end": 119.519}, {"text": "rozbiť", "start": 119.519, "end": 119.899}, {"text": " ", "start": 119.899, "end": 119.998}, {"text": "tri", "start": 119.998, "end": 120.099}, {"text": " ", "start": 120.099, "end": 120.138}, {"text": "vajíčka", "start": 120.138, "end": 120.598}, {"text": " ", "start": 120.598, "end": 120.639}, {"text": "do", "start": 120.639, "end": 120.718}, {"text": " ", "start": 120.718, "end": 120.758}, {"text": "tejto", "start": 120.758, "end": 121.018}, {"text": " ", "start": 121.018, "end": 121.059}, {"text": "misky?\"", "start": 121.059, "end": 121.418}, {"text": "\n", "start": 121.418, "end": 122.679}, {"text": "\n", "start": 122.679, "end": 122.699}, {"text": "Len<PERSON>", "start": 122.699, "end": 122.959}, {"text": " ", "start": 122.959, "end": 123.038}, {"text": "sa", "start": 123.038, "end": 123.138}, {"text": " ", "start": 123.138, "end": 123.179}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>,", "start": 123.179, "end": 123.838}, {"text": " ", "start": 123.838, "end": 123.939}, {"text": "akoby", "start": 123.939, "end": 124.258}, {"text": " ", "start": 124.258, "end": 124.298}, {"text": "<PERSON><PERSON><PERSON>", "start": 124.298, "end": 124.558}, {"text": " ", "start": 124.558, "end": 124.598}, {"text": "o", "start": 124.598, "end": 124.658}, {"text": " ", "start": 124.658, "end": 124.698}, {"text": "najd<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 124.698, "end": 125.618}, {"text": " ", "start": 125.618, "end": 125.658}, {"text": "vec", "start": 125.658, "end": 125.858}, {"text": " ", "start": 125.858, "end": 125.899}, {"text": "na", "start": 125.899, "end": 125.998}, {"text": " ", "start": 125.998, "end": 126.078}, {"text": "svete.", "start": 126.078, "end": 126.419}, {"text": " ", "start": 126.419, "end": 127.318}, {"text": "Opatrne", "start": 127.318, "end": 127.798}, {"text": " ", "start": 127.798, "end": 127.838}, {"text": "r<PERSON><PERSON>a", "start": 127.838, "end": 128.259}, {"text": " ", "start": 128.259, "end": 128.318}, {"text": "kaž<PERSON><PERSON>", "start": 128.318, "end": 128.638}, {"text": " ", "start": 128.638, "end": 128.698}, {"text": "vajce,", "start": 128.698, "end": 129.098}, {"text": " ", "start": 129.098, "end": 129.558}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 129.558, "end": 130.078}, {"text": " ", "start": 130.078, "end": 130.098}, {"text": "si", "start": 130.098, "end": 130.178}, {"text": " ", "start": 130.178, "end": 130.238}, {"text": "veľký", "start": 130.238, "end": 130.558}, {"text": " ", "start": 130.558, "end": 130.678}, {"text": "pozor,", "start": 130.678, "end": 130.998}, {"text": " ", "start": 130.998, "end": 131.398}, {"text": "aby", "start": 131.398, "end": 131.538}, {"text": " ", "start": 131.538, "end": 131.578}, {"text": "sa", "start": 131.578, "end": 131.678}, {"text": " ", "start": 131.678, "end": 131.718}, {"text": "do", "start": 131.718, "end": 131.799}, {"text": " ", "start": 131.799, "end": 131.858}, {"text": "misky", "start": 131.858, "end": 132.118}, {"text": " ", "start": 132.118, "end": 132.158}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 132.158, "end": 132.658}, {"text": " ", "start": 132.658, "end": 132.718}, {"text": "žiadne", "start": 132.718, "end": 133.038}, {"text": " ", "start": 133.038, "end": 133.098}, {"text": "<PERSON><PERSON><PERSON>", "start": 133.098, "end": 133.438}, {"text": " ", "start": 133.438, "end": 133.498}, {"text": "škrupiny.", "start": 133.498, "end": 134.138}, {"text": "\n", "start": 134.138, "end": 135.438}, {"text": "\n", "start": 135.438, "end": 135.459}, {"text": "„Výborne!", "start": 135.459, "end": 135.979}, {"text": " ", "start": 135.979, "end": 136.459}, {"text": "Teraz", "start": 136.459, "end": 136.698}, {"text": " ", "start": 136.698, "end": 136.719}, {"text": "vyšľaháme", "start": 136.719, "end": 137.278}, {"text": " ", "start": 137.278, "end": 137.318}, {"text": "vajíčka", "start": 137.318, "end": 137.738}, {"text": " ", "start": 137.738, "end": 137.799}, {"text": "s", "start": 137.799, "end": 137.838}, {"text": " ", "start": 137.838, "end": 137.938}, {"text": "cukrom.\"", "start": 137.938, "end": 138.358}, {"text": " ", "start": 138.358, "end": 139.118}, {"text": "<PERSON>", "start": 139.118, "end": 139.378}, {"text": " ", "start": 139.378, "end": 139.418}, {"text": "jej", "start": 139.418, "end": 139.598}, {"text": " ", "start": 139.598, "end": 139.678}, {"text": "ukázala,", "start": 139.678, "end": 140.219}, {"text": " ", "start": 140.219, "end": 140.318}, {"text": "ako", "start": 140.318, "end": 140.479}, {"text": " ", "start": 140.479, "end": 140.538}, {"text": "drž<PERSON><PERSON>", "start": 140.538, "end": 140.858}, {"text": " ", "start": 140.858, "end": 140.918}, {"text": "šľahač", "start": 140.918, "end": 141.538}, {"text": " ", "start": 141.538, "end": 141.618}, {"text": "a", "start": 141.618, "end": 141.658}, {"text": " ", "start": 141.658, "end": 141.719}, {"text": "spolu", "start": 141.719, "end": 141.959}, {"text": " ", "start": 141.959, "end": 141.998}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 141.998, "end": 142.498}, {"text": " ", "start": 142.498, "end": 142.538}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>,", "start": 142.538, "end": 143.038}, {"text": " ", "start": 143.038, "end": 143.278}, {"text": "a<PERSON>", "start": 143.278, "end": 143.398}, {"text": " ", "start": 143.398, "end": 143.459}, {"text": "kým", "start": 143.459, "end": 143.559}, {"text": " ", "start": 143.559, "end": 143.598}, {"text": "neboli", "start": 143.598, "end": 143.898}, {"text": " ", "start": 143.898, "end": 143.959}, {"text": "na<PERSON><PERSON><PERSON><PERSON>", "start": 143.959, "end": 144.598}, {"text": " ", "start": 144.598, "end": 144.658}, {"text": "a", "start": 144.658, "end": 144.698}, {"text": " ", "start": 144.698, "end": 144.799}, {"text": "s<PERSON><PERSON>.", "start": 144.799, "end": 145.378}, {"text": "\n", "start": 145.378, "end": 146.459}, {"text": "\n", "start": 146.459, "end": 146.479}, {"text": "„Teraz", "start": 146.479, "end": 146.838}, {"text": " ", "start": 146.838, "end": 146.879}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 146.879, "end": 147.318}, {"text": " ", "start": 147.318, "end": 147.379}, {"text": "pol", "start": 147.379, "end": 147.518}, {"text": " ", "start": 147.518, "end": 147.578}, {"text": "hrnčeka", "start": 147.578, "end": 147.998}, {"text": " ", "start": 147.998, "end": 148.058}, {"text": "mlieka,\"", "start": 148.058, "end": 148.478}, {"text": " ", "start": 148.478, "end": 149.219}, {"text": "povedala", "start": 149.219, "end": 149.638}, {"text": " ", "start": 149.638, "end": 149.698}, {"text": "mama", "start": 149.698, "end": 150.299}, {"text": " ", "start": 150.299, "end": 150.358}, {"text": "a", "start": 150.358, "end": 150.418}, {"text": " ", "start": 150.418, "end": 150.478}, {"text": "podala", "start": 150.478, "end": 150.799}, {"text": " ", "start": 150.799, "end": 150.858}, {"text": "<PERSON><PERSON>", "start": 150.858, "end": 151.138}, {"text": " ", "start": 151.138, "end": 151.178}, {"text": "<PERSON><PERSON><PERSON><PERSON>.", "start": 151.178, "end": 151.698}, {"text": "\n", "start": 151.698, "end": 152.639}, {"text": "\n", "start": 152.639, "end": 152.718}, {"text": "Len<PERSON>", "start": 152.718, "end": 153.118}, {"text": " ", "start": 153.118, "end": 153.178}, {"text": "naliala", "start": 153.178, "end": 153.639}, {"text": " ", "start": 153.639, "end": 153.678}, {"text": "mlieko", "start": 153.678, "end": 154.358}, {"text": " ", "start": 154.358, "end": 154.418}, {"text": "presne", "start": 154.418, "end": 154.799}, {"text": " ", "start": 154.799, "end": 154.839}, {"text": "po", "start": 154.839, "end": 154.938}, {"text": " ", "start": 154.938, "end": 154.999}, {"text": "<PERSON><PERSON><PERSON>", "start": 154.999, "end": 155.418}, {"text": " ", "start": 155.418, "end": 155.459}, {"text": "hrnčeka", "start": 155.459, "end": 156.518}, {"text": " ", "start": 156.518, "end": 156.598}, {"text": "a", "start": 156.598, "end": 156.639}, {"text": " ", "start": 156.639, "end": 156.698}, {"text": "potom", "start": 156.698, "end": 156.959}, {"text": " ", "start": 156.959, "end": 156.999}, {"text": "ho", "start": 156.999, "end": 157.118}, {"text": " ", "start": 157.118, "end": 157.198}, {"text": "opatrne", "start": 157.198, "end": 157.718}, {"text": " ", "start": 157.718, "end": 157.758}, {"text": "pridala", "start": 157.758, "end": 158.159}, {"text": " ", "start": 158.159, "end": 158.218}, {"text": "do", "start": 158.218, "end": 158.299}, {"text": " ", "start": 158.299, "end": 158.338}, {"text": "misky.", "start": 158.338, "end": 158.758}, {"text": "\n", "start": 158.758, "end": 159.758}, {"text": "\n", "start": 159.758, "end": 159.778}, {"text": "„A", "start": 159.778, "end": 159.878}, {"text": " ", "start": 159.878, "end": 159.918}, {"text": "teraz", "start": 159.918, "end": 160.178}, {"text": " ", "start": 160.178, "end": 160.218}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 160.218, "end": 160.819}, {"text": " ", "start": 160.819, "end": 160.878}, {"text": "maslo,", "start": 160.878, "end": 161.358}, {"text": " ", "start": 161.358, "end": 161.678}, {"text": "jeden", "start": 161.678, "end": 161.918}, {"text": " ", "start": 161.918, "end": 161.959}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 161.959, "end": 162.338}, {"text": " ", "start": 162.338, "end": 162.398}, {"text": "m<PERSON><PERSON>,", "start": 162.398, "end": 162.838}, {"text": " ", "start": 162.838, "end": 163.178}, {"text": "tri", "start": 163.178, "end": 163.338}, {"text": " ", "start": 163.338, "end": 163.398}, {"text": "lyžice", "start": 163.398, "end": 163.778}, {"text": " ", "start": 163.778, "end": 163.838}, {"text": "kakaa", "start": 163.838, "end": 164.678}, {"text": " ", "start": 164.678, "end": 164.758}, {"text": "a", "start": 164.758, "end": 164.778}, {"text": " ", "start": 164.778, "end": 164.838}, {"text": "lyžičku", "start": 164.838, "end": 165.258}, {"text": " ", "start": 165.258, "end": 165.299}, {"text": "práš<PERSON>", "start": 165.299, "end": 165.698}, {"text": " ", "start": 165.698, "end": 165.738}, {"text": "do", "start": 165.738, "end": 165.838}, {"text": " ", "start": 165.838, "end": 165.878}, {"text": "pe<PERSON><PERSON>,\"", "start": 165.878, "end": 166.299}, {"text": " ", "start": 166.299, "end": 167.038}, {"text": "povedala", "start": 167.038, "end": 167.459}, {"text": " ", "start": 167.459, "end": 167.518}, {"text": "mama,", "start": 167.518, "end": 167.799}, {"text": " ", "start": 167.799, "end": 167.998}, {"text": "podávajúc", "start": 167.998, "end": 168.518}, {"text": " ", "start": 168.518, "end": 168.639}, {"text": "<PERSON><PERSON>", "start": 168.639, "end": 168.959}, {"text": " ", "start": 168.959, "end": 169.018}, {"text": "každú", "start": 169.018, "end": 169.319}, {"text": " ", "start": 169.319, "end": 169.358}, {"text": "prísadu.", "start": 169.358, "end": 169.878}, {"text": "\n", "start": 169.878, "end": 169.999}, {"text": "\n", "start": 169.999, "end": 171.098}, {"text": "Len<PERSON>", "start": 171.098, "end": 171.538}, {"text": " ", "start": 171.538, "end": 171.598}, {"text": "pridala", "start": 171.598, "end": 171.938}, {"text": " ", "start": 171.938, "end": 171.998}, {"text": "všetko", "start": 171.998, "end": 172.299}, {"text": " ", "start": 172.299, "end": 172.358}, {"text": "do", "start": 172.358, "end": 172.418}, {"text": " ", "start": 172.418, "end": 172.479}, {"text": "misky", "start": 172.479, "end": 173.018}, {"text": " ", "start": 173.018, "end": 173.098}, {"text": "a", "start": 173.098, "end": 173.139}, {"text": " ", "start": 173.139, "end": 173.178}, {"text": "potom", "start": 173.178, "end": 173.378}, {"text": " ", "start": 173.378, "end": 173.438}, {"text": "sa", "start": 173.438, "end": 173.518}, {"text": " ", "start": 173.518, "end": 173.598}, {"text": "spýtala:", "start": 173.598, "end": 174.138}, {"text": " ", "start": 174.138, "end": 174.178}, {"text": "<PERSON><PERSON><PERSON>,", "start": 174.178, "end": 175.998}, {"text": " ", "start": 175.998, "end": 176.098}, {"text": "<PERSON><PERSON><PERSON>", "start": 176.098, "end": 176.438}, {"text": " ", "start": 176.438, "end": 176.479}, {"text": "nepotrebujeme", "start": 176.479, "end": 177.139}, {"text": " ", "start": 177.139, "end": 177.238}, {"text": "špeciálne", "start": 177.238, "end": 177.778}, {"text": " ", "start": 177.778, "end": 177.858}, {"text": "odmer<PERSON>", "start": 177.858, "end": 178.318}, {"text": " ", "start": 178.318, "end": 178.398}, {"text": "ako", "start": 178.398, "end": 178.538}, {"text": " ", "start": 178.538, "end": 178.578}, {"text": "v", "start": 178.578, "end": 178.598}, {"text": " ", "start": 178.598, "end": 178.658}, {"text": "televízii?\"", "start": 178.658, "end": 179.438}, {"text": "\n", "start": 179.438, "end": 180.538}, {"text": "\n", "start": 180.538, "end": 180.658}, {"text": "<PERSON>", "start": 180.658, "end": 180.778}, {"text": " ", "start": 180.778, "end": 180.838}, {"text": "sa", "start": 180.838, "end": 180.938}, {"text": " ", "start": 180.938, "end": 180.979}, {"text": "zas<PERSON>la.", "start": 180.979, "end": 181.658}, {"text": " ", "start": 181.658, "end": 182.358}, {"text": "„To", "start": 182.358, "end": 182.479}, {"text": " ", "start": 182.479, "end": 182.518}, {"text": "je", "start": 182.518, "end": 182.639}, {"text": " ", "start": 182.639, "end": 182.678}, {"text": "práve", "start": 182.678, "end": 182.979}, {"text": " ", "start": 182.979, "end": 183.038}, {"text": "to", "start": 183.038, "end": 183.139}, {"text": " ", "start": 183.139, "end": 183.198}, {"text": "najlepšie", "start": 183.198, "end": 183.778}, {"text": " ", "start": 183.778, "end": 183.838}, {"text": "na", "start": 183.838, "end": 183.918}, {"text": " ", "start": 183.918, "end": 183.959}, {"text": "hrnčekovom", "start": 183.959, "end": 184.578}, {"text": " ", "start": 184.578, "end": 184.639}, {"text": "kol<PERSON><PERSON>i.", "start": 184.639, "end": 185.219}, {"text": " ", "start": 185.219, "end": 185.778}, {"text": "Je", "start": 185.778, "end": 185.859}, {"text": " ", "start": 185.859, "end": 185.918}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 185.918, "end": 186.719}, {"text": " ", "start": 186.719, "end": 186.798}, {"text": "a", "start": 186.798, "end": 186.838}, {"text": " ", "start": 186.838, "end": 186.898}, {"text": "vždy", "start": 186.898, "end": 187.179}, {"text": " ", "start": 187.179, "end": 187.238}, {"text": "sa", "start": 187.238, "end": 187.338}, {"text": " ", "start": 187.338, "end": 187.378}, {"text": "vydarí.", "start": 187.378, "end": 187.918}, {"text": " ", "start": 187.918, "end": 188.478}, {"text": "<PERSON><PERSON>", "start": 188.478, "end": 188.758}, {"text": " ", "start": 188.758, "end": 188.798}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 188.798, "end": 189.218}, {"text": " ", "start": 189.218, "end": 189.278}, {"text": "m<PERSON>ky", "start": 189.278, "end": 189.858}, {"text": " ", "start": 189.858, "end": 189.938}, {"text": "je", "start": 189.938, "end": 190.038}, {"text": " ", "start": 190.038, "end": 190.098}, {"text": "jeden", "start": 190.098, "end": 190.378}, {"text": " ", "start": 190.378, "end": 190.438}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 190.438, "end": 190.859}, {"text": " ", "start": 190.859, "end": 190.918}, {"text": "m<PERSON><PERSON>,", "start": 190.918, "end": 191.658}, {"text": " ", "start": 191.658, "end": 191.738}, {"text": "nech", "start": 191.738, "end": 191.898}, {"text": " ", "start": 191.898, "end": 191.978}, {"text": "už", "start": 191.978, "end": 192.058}, {"text": " ", "start": 192.058, "end": 192.118}, {"text": "je", "start": 192.118, "end": 192.179}, {"text": " ", "start": 192.179, "end": 192.219}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 192.219, "end": 192.578}, {"text": " ", "start": 192.578, "end": 192.618}, {"text": "akýkoľvek.", "start": 192.618, "end": 193.319}, {"text": " ", "start": 193.319, "end": 194.039}, {"text": "<PERSON><PERSON><PERSON>j<PERSON>,", "start": 194.039, "end": 194.638}, {"text": " ", "start": 194.638, "end": 195.058}, {"text": "vždy", "start": 195.058, "end": 195.338}, {"text": " ", "start": 195.338, "end": 195.398}, {"text": "pou<PERSON><PERSON><PERSON><PERSON>", "start": 195.398, "end": 195.998}, {"text": " ", "start": 195.998, "end": 196.078}, {"text": "ten", "start": 196.078, "end": 196.258}, {"text": " ", "start": 196.258, "end": 196.298}, {"text": "istý", "start": 196.298, "end": 196.578}, {"text": " ", "start": 196.578, "end": 196.639}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 196.639, "end": 197.158}, {"text": " ", "start": 197.158, "end": 197.218}, {"text": "pre", "start": 197.218, "end": 197.359}, {"text": " ", "start": 197.359, "end": 197.438}, {"text": "celý", "start": 197.438, "end": 197.678}, {"text": " ", "start": 197.678, "end": 197.718}, {"text": "recept.\"", "start": 197.718, "end": 198.198}, {"text": "\n", "start": 198.198, "end": 199.118}, {"text": "\n", "start": 199.118, "end": 199.198}, {"text": "Spolu", "start": 199.198, "end": 199.538}, {"text": " ", "start": 199.538, "end": 199.618}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 199.618, "end": 200.078}, {"text": " ", "start": 200.078, "end": 200.158}, {"text": "cesto,", "start": 200.158, "end": 200.758}, {"text": " ", "start": 200.758, "end": 200.999}, {"text": "a<PERSON>", "start": 200.999, "end": 201.118}, {"text": " ", "start": 201.118, "end": 201.179}, {"text": "kým", "start": 201.179, "end": 201.298}, {"text": " ", "start": 201.298, "end": 201.338}, {"text": "nebolo", "start": 201.338, "end": 201.658}, {"text": " ", "start": 201.658, "end": 201.698}, {"text": "h<PERSON><PERSON><PERSON>", "start": 201.698, "end": 202.338}, {"text": " ", "start": 202.338, "end": 202.378}, {"text": "a", "start": 202.378, "end": 202.459}, {"text": " ", "start": 202.459, "end": 202.518}, {"text": "tmavé", "start": 202.518, "end": 202.958}, {"text": " ", "start": 202.958, "end": 203.018}, {"text": "od", "start": 203.018, "end": 203.138}, {"text": " ", "start": 203.138, "end": 203.198}, {"text": "kakaa.", "start": 203.198, "end": 203.818}, {"text": "\n", "start": 203.818, "end": 204.898}, {"text": "\n", "start": 204.898, "end": 204.918}, {"text": "„Teraz", "start": 204.918, "end": 205.218}, {"text": " ", "start": 205.218, "end": 205.258}, {"text": "ho", "start": 205.258, "end": 205.338}, {"text": " ", "start": 205.338, "end": 205.378}, {"text": "vylejeme", "start": 205.378, "end": 205.818}, {"text": " ", "start": 205.818, "end": 205.859}, {"text": "do", "start": 205.859, "end": 205.959}, {"text": " ", "start": 205.959, "end": 206.039}, {"text": "formy,\"", "start": 206.039, "end": 206.358}, {"text": " ", "start": 206.358, "end": 207.039}, {"text": "povedala", "start": 207.039, "end": 207.458}, {"text": " ", "start": 207.458, "end": 207.499}, {"text": "mama", "start": 207.499, "end": 208.039}, {"text": " ", "start": 208.039, "end": 208.118}, {"text": "a", "start": 208.118, "end": 208.158}, {"text": " ", "start": 208.158, "end": 208.218}, {"text": "pomohla", "start": 208.218, "end": 208.558}, {"text": " ", "start": 208.558, "end": 208.618}, {"text": "<PERSON><PERSON>", "start": 208.618, "end": 208.918}, {"text": " ", "start": 208.918, "end": 208.978}, {"text": "preliať", "start": 208.978, "end": 209.378}, {"text": " ", "start": 209.378, "end": 209.459}, {"text": "cesto", "start": 209.459, "end": 209.738}, {"text": " ", "start": 209.738, "end": 209.778}, {"text": "do", "start": 209.778, "end": 209.838}, {"text": " ", "start": 209.838, "end": 209.878}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 209.878, "end": 210.478}, {"text": " ", "start": 210.478, "end": 210.538}, {"text": "formy.", "start": 210.538, "end": 210.999}, {"text": "\n", "start": 210.999, "end": 211.838}, {"text": "\n", "start": 211.838, "end": 211.858}, {"text": "„A", "start": 211.858, "end": 211.978}, {"text": " ", "start": 211.978, "end": 212.038}, {"text": "už", "start": 212.038, "end": 212.118}, {"text": " ", "start": 212.118, "end": 212.179}, {"text": "ide", "start": 212.179, "end": 212.319}, {"text": " ", "start": 212.319, "end": 212.378}, {"text": "do", "start": 212.378, "end": 212.478}, {"text": " ", "start": 212.478, "end": 212.538}, {"text": "rú<PERSON>?\"", "start": 212.538, "end": 212.818}, {"text": " ", "start": 212.818, "end": 213.558}, {"text": "s<PERSON><PERSON><PERSON><PERSON>", "start": 213.558, "end": 214.018}, {"text": " ", "start": 214.018, "end": 214.058}, {"text": "sa", "start": 214.058, "end": 214.158}, {"text": " ", "start": 214.158, "end": 214.198}, {"text": "Len<PERSON>", "start": 214.198, "end": 214.478}, {"text": " ", "start": 214.478, "end": 214.518}, {"text": "nadšene.", "start": 214.518, "end": 215.158}, {"text": "\n", "start": 215.158, "end": 216.078}, {"text": "\n", "start": 216.078, "end": 216.098}, {"text": "„Presne", "start": 216.098, "end": 216.459}, {"text": " ", "start": 216.459, "end": 216.518}, {"text": "tak.", "start": 216.518, "end": 216.698}, {"text": " ", "start": 216.698, "end": 217.298}, {"text": "Pri", "start": 217.298, "end": 217.438}, {"text": " ", "start": 217.438, "end": 217.718}, {"text": "180", "start": 217.718, "end": 218.278}, {"text": " ", "start": 218.278, "end": 218.338}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 218.338, "end": 219.158}, {"text": " ", "start": 219.158, "end": 219.218}, {"text": "približne", "start": 219.218, "end": 219.718}, {"text": " ", "start": 219.718, "end": 219.959}, {"text": "30", "start": 219.959, "end": 220.178}, {"text": " ", "start": 220.178, "end": 220.218}, {"text": "minút.\"", "start": 220.218, "end": 220.718}, {"text": "\n", "start": 220.718, "end": 221.758}, {"text": "\n", "start": 221.758, "end": 221.798}, {"text": "<PERSON><PERSON><PERSON>", "start": 221.798, "end": 221.938}, {"text": " ", "start": 221.938, "end": 221.999}, {"text": "sa", "start": 221.999, "end": 222.098}, {"text": " ", "start": 222.098, "end": 222.158}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 222.158, "end": 222.518}, {"text": " ", "start": 222.518, "end": 222.558}, {"text": "piekol", "start": 222.558, "end": 223.018}, {"text": " ", "start": 223.018, "end": 223.078}, {"text": "a", "start": 223.078, "end": 223.118}, {"text": " ", "start": 223.118, "end": 223.179}, {"text": "naplnil", "start": 223.179, "end": 223.638}, {"text": " ", "start": 223.638, "end": 223.698}, {"text": "kuchyňu", "start": 223.698, "end": 224.078}, {"text": " ", "start": 224.078, "end": 224.118}, {"text": "b<PERSON>žsko<PERSON>", "start": 224.118, "end": 224.578}, {"text": " ", "start": 224.578, "end": 224.618}, {"text": "vô<PERSON><PERSON>", "start": 224.618, "end": 225.018}, {"text": " ", "start": 225.018, "end": 225.078}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>,", "start": 225.078, "end": 226.158}, {"text": " ", "start": 226.158, "end": 226.258}, {"text": "mama", "start": 226.258, "end": 226.478}, {"text": " ", "start": 226.478, "end": 226.558}, {"text": "um<PERSON>", "start": 226.558, "end": 226.838}, {"text": " ", "start": 226.838, "end": 226.898}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 226.898, "end": 227.578}, {"text": " ", "start": 227.578, "end": 227.679}, {"text": "a", "start": 227.679, "end": 227.698}, {"text": " ", "start": 227.698, "end": 227.738}, {"text": "začala", "start": 227.738, "end": 228.078}, {"text": " ", "start": 228.078, "end": 228.118}, {"text": "pripravovať", "start": 228.118, "end": 228.738}, {"text": " ", "start": 228.738, "end": 228.858}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 228.858, "end": 229.158}, {"text": " ", "start": 229.158, "end": 229.238}, {"text": "ingrediencie.", "start": 229.238, "end": 231.358}, {"text": "\n", "start": 231.358, "end": 231.398}, {"text": "\n", "start": 231.398, "end": 231.418}, {"text": "„Čo", "start": 231.418, "end": 231.558}, {"text": " ", "start": 231.558, "end": 231.598}, {"text": "r<PERSON><PERSON><PERSON>", "start": 231.598, "end": 231.918}, {"text": " ", "start": 231.918, "end": 232.018}, {"text": "teraz?\"", "start": 232.018, "end": 233.338}, {"text": " ", "start": 233.338, "end": 233.398}, {"text": "Len<PERSON>", "start": 233.398, "end": 233.658}, {"text": " ", "start": 233.658, "end": 233.738}, {"text": "st<PERSON><PERSON>", "start": 233.738, "end": 234.038}, {"text": " ", "start": 234.038, "end": 234.078}, {"text": "na", "start": 234.078, "end": 234.178}, {"text": " ", "start": 234.178, "end": 234.238}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,", "start": 234.238, "end": 235.018}, {"text": " ", "start": 235.018, "end": 235.118}, {"text": "aby", "start": 235.118, "end": 235.278}, {"text": " ", "start": 235.278, "end": 235.319}, {"text": "videla", "start": 235.319, "end": 235.639}, {"text": " ", "start": 235.639, "end": 235.718}, {"text": "cez", "start": 235.718, "end": 235.878}, {"text": " ", "start": 235.878, "end": 235.999}, {"text": "<PERSON><PERSON><PERSON>", "start": 235.999, "end": 236.358}, {"text": " ", "start": 236.358, "end": 236.418}, {"text": "na", "start": 236.418, "end": 236.518}, {"text": " ", "start": 236.518, "end": 236.578}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 236.578, "end": 236.938}, {"text": " ", "start": 236.938, "end": 236.978}, {"text": "v", "start": 236.978, "end": 236.998}, {"text": " ", "start": 236.998, "end": 237.038}, {"text": "<PERSON><PERSON><PERSON>.", "start": 237.038, "end": 238.358}, {"text": "\n", "start": 238.358, "end": 238.398}, {"text": "\n", "start": 238.398, "end": 238.418}, {"text": "„Teraz", "start": 238.418, "end": 238.718}, {"text": " ", "start": 238.718, "end": 238.758}, {"text": "pripravíme", "start": 238.758, "end": 239.278}, {"text": " ", "start": 239.278, "end": 239.338}, {"text": "svetlú", "start": 239.338, "end": 239.718}, {"text": " ", "start": 239.718, "end": 239.738}, {"text": "časť", "start": 239.738, "end": 240.318}, {"text": " ", "start": 240.318, "end": 240.338}, {"text": "–", "start": 240.338, "end": 240.339}, {"text": " ", "start": 240.339, "end": 240.378}, {"text": "de<PERSON>,\"", "start": 240.378, "end": 241.398}, {"text": " ", "start": 241.398, "end": 241.459}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 241.459, "end": 241.978}, {"text": " ", "start": 241.978, "end": 242.018}, {"text": "mama.", "start": 242.018, "end": 243.198}, {"text": " ", "start": 243.198, "end": 243.238}, {"text": "„Potrebujeme", "start": 243.238, "end": 243.838}, {"text": " ", "start": 243.838, "end": 243.938}, {"text": "t<PERSON>h,", "start": 243.938, "end": 244.578}, {"text": " ", "start": 244.578, "end": 244.698}, {"text": "s<PERSON><PERSON>", "start": 244.698, "end": 245.459}, {"text": " ", "start": 245.459, "end": 245.538}, {"text": "a", "start": 245.538, "end": 245.558}, {"text": " ", "start": 245.558, "end": 245.618}, {"text": "broskyne.\"", "start": 245.618, "end": 247.758}, {"text": "\n", "start": 247.758, "end": 247.819}, {"text": "\n", "start": 247.819, "end": 247.838}, {"text": "Keď", "start": 247.838, "end": 247.979}, {"text": " ", "start": 247.979, "end": 248.018}, {"text": "sa", "start": 248.018, "end": 248.098}, {"text": " ", "start": 248.098, "end": 248.158}, {"text": "č<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 248.158, "end": 248.738}, {"text": " ", "start": 248.738, "end": 248.798}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 248.798, "end": 249.158}, {"text": " ", "start": 249.158, "end": 249.238}, {"text": "upiekol", "start": 249.238, "end": 249.718}, {"text": " ", "start": 249.718, "end": 249.798}, {"text": "a", "start": 249.798, "end": 249.819}, {"text": " ", "start": 249.819, "end": 249.858}, {"text": "vychladol,", "start": 249.858, "end": 250.918}, {"text": " ", "start": 250.918, "end": 251.018}, {"text": "mama", "start": 251.018, "end": 251.338}, {"text": " ", "start": 251.338, "end": 251.358}, {"text": "ukázala", "start": 251.358, "end": 251.838}, {"text": " ", "start": 251.838, "end": 251.878}, {"text": "<PERSON><PERSON>,", "start": 251.878, "end": 252.658}, {"text": " ", "start": 252.658, "end": 252.759}, {"text": "ako", "start": 252.759, "end": 252.938}, {"text": " ", "start": 252.938, "end": 252.979}, {"text": "pripraviť", "start": 252.979, "end": 253.478}, {"text": " ", "start": 253.478, "end": 253.539}, {"text": "krémovú", "start": 253.539, "end": 253.959}, {"text": " ", "start": 253.959, "end": 253.998}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 253.998, "end": 254.498}, {"text": " ", "start": 254.498, "end": 254.638}, {"text": "zmes", "start": 254.638, "end": 255.298}, {"text": " ", "start": 255.298, "end": 255.319}, {"text": "a", "start": 255.319, "end": 255.358}, {"text": " ", "start": 255.358, "end": 255.399}, {"text": "natrieť", "start": 255.399, "end": 255.878}, {"text": " ", "start": 255.878, "end": 255.879}, {"text": "ju", "start": 255.879, "end": 255.918}, {"text": " ", "start": 255.918, "end": 255.959}, {"text": "na", "start": 255.959, "end": 256.078}, {"text": " ", "start": 256.078, "end": 256.119}, {"text": "kol<PERSON>č.", "start": 256.119, "end": 257.518}, {"text": "\n", "start": 257.518, "end": 257.558}, {"text": "\n", "start": 257.558, "end": 257.578}, {"text": "„Teraz", "start": 257.578, "end": 257.838}, {"text": " ", "start": 257.838, "end": 257.918}, {"text": "musí", "start": 257.918, "end": 258.158}, {"text": " ", "start": 258.158, "end": 258.198}, {"text": "ísť", "start": 258.198, "end": 258.358}, {"text": " ", "start": 258.358, "end": 258.378}, {"text": "do", "start": 258.378, "end": 258.459}, {"text": " ", "start": 258.459, "end": 258.479}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>,\"", "start": 258.479, "end": 259.418}, {"text": " ", "start": 259.418, "end": 259.479}, {"text": "povedala", "start": 259.479, "end": 259.858}, {"text": " ", "start": 259.858, "end": 259.918}, {"text": "mama.", "start": 259.918, "end": 260.259}, {"text": "\n", "start": 260.259, "end": 260.918}, {"text": "\n", "start": 260.918, "end": 260.938}, {"text": "„Na", "start": 260.938, "end": 261.058}, {"text": " ", "start": 261.058, "end": 261.179}, {"text": "ako", "start": 261.179, "end": 261.278}, {"text": " ", "start": 261.278, "end": 261.338}, {"text": "dlho?\"", "start": 261.338, "end": 262.158}, {"text": " ", "start": 262.158, "end": 262.319}, {"text": "s<PERSON><PERSON><PERSON><PERSON>", "start": 262.319, "end": 262.658}, {"text": " ", "start": 262.658, "end": 262.718}, {"text": "sa", "start": 262.718, "end": 262.778}, {"text": " ", "start": 262.778, "end": 262.838}, {"text": "Len<PERSON>", "start": 262.838, "end": 263.358}, {"text": " ", "start": 263.358, "end": 263.378}, {"text": "a", "start": 263.378, "end": 263.399}, {"text": " ", "start": 263.399, "end": 263.438}, {"text": "pov<PERSON><PERSON><PERSON>la", "start": 263.438, "end": 263.998}, {"text": " ", "start": 263.998, "end": 264.078}, {"text": "si.", "start": 264.078, "end": 264.918}, {"text": "\n", "start": 264.918, "end": 264.959}, {"text": "\n", "start": 264.959, "end": 264.96}, {"text": "„Len", "start": 264.96, "end": 265.278}, {"text": " ", "start": 265.278, "end": 265.399}, {"text": "na", "start": 265.399, "end": 265.438}, {"text": " ", "start": 265.438, "end": 265.498}, {"text": "hodinu,\"", "start": 265.498, "end": 266.679}, {"text": " ", "start": 266.679, "end": 266.838}, {"text": "ubezpečila", "start": 266.838, "end": 267.458}, {"text": " ", "start": 267.458, "end": 267.518}, {"text": "ju", "start": 267.518, "end": 267.578}, {"text": " ", "start": 267.578, "end": 267.638}, {"text": "mama.", "start": 267.638, "end": 269.039}, {"text": "\n", "start": 269.039, "end": 269.078}, {"text": "\n", "start": 269.078, "end": 269.098}, {"text": "Neskôr", "start": 269.098, "end": 269.738}, {"text": " ", "start": 269.738, "end": 270.039}, {"text": "Len<PERSON>", "start": 270.039, "end": 270.138}, {"text": " ", "start": 270.138, "end": 270.179}, {"text": "pomáhala", "start": 270.179, "end": 270.778}, {"text": " ", "start": 270.778, "end": 270.798}, {"text": "mame", "start": 270.798, "end": 271.179}, {"text": " ", "start": 271.179, "end": 271.259}, {"text": "ukladať", "start": 271.259, "end": 271.678}, {"text": " ", "start": 271.678, "end": 271.738}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 271.738, "end": 272.118}, {"text": " ", "start": 272.118, "end": 272.158}, {"text": "broskýň", "start": 272.158, "end": 272.838}, {"text": " ", "start": 272.838, "end": 272.899}, {"text": "na", "start": 272.899, "end": 272.998}, {"text": " ", "start": 272.998, "end": 273.039}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 273.039, "end": 273.598}, {"text": " ", "start": 273.598, "end": 273.638}, {"text": "vrstvu.", "start": 273.638, "end": 274.898}, {"text": " ", "start": 274.898, "end": 274.998}, {"text": "<PERSON><PERSON>", "start": 274.998, "end": 275.238}, {"text": " ", "start": 275.238, "end": 275.319}, {"text": "zalia<PERSON>", "start": 275.319, "end": 275.698}, {"text": " ", "start": 275.698, "end": 275.759}, {"text": "broskyne", "start": 275.759, "end": 276.238}, {"text": " ", "start": 276.238, "end": 276.358}, {"text": "žiarivým", "start": 276.358, "end": 276.778}, {"text": " ", "start": 276.778, "end": 276.838}, {"text": "želé.", "start": 276.838, "end": 278.378}, {"text": "\n", "start": 278.378, "end": 278.418}, {"text": "\n", "start": 278.418, "end": 278.438}, {"text": "„A", "start": 278.438, "end": 278.518}, {"text": " ", "start": 278.518, "end": 278.558}, {"text": "<PERSON><PERSON>", "start": 278.558, "end": 278.798}, {"text": " ", "start": 278.798, "end": 278.838}, {"text": "musí", "start": 278.838, "end": 279.038}, {"text": " ", "start": 279.038, "end": 279.078}, {"text": "do", "start": 279.078, "end": 279.158}, {"text": " ", "start": 279.158, "end": 279.179}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>?\"", "start": 279.179, "end": 280.218}, {"text": " ", "start": 280.218, "end": 280.319}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 280.319, "end": 280.698}, {"text": " ", "start": 280.698, "end": 280.918}, {"text": "Lenka.", "start": 280.918, "end": 281.798}, {"text": "\n", "start": 281.798, "end": 281.838}, {"text": "\n", "start": 281.838, "end": 281.858}, {"text": "„Áno,", "start": 281.858, "end": 282.378}, {"text": " ", "start": 282.378, "end": 282.518}, {"text": "už", "start": 282.518, "end": 282.578}, {"text": " ", "start": 282.578, "end": 282.638}, {"text": "vieš", "start": 282.638, "end": 282.838}, {"text": " ", "start": 282.838, "end": 282.899}, {"text": "ako", "start": 282.899, "end": 283.058}, {"text": " ", "start": 283.058, "end": 283.098}, {"text": "na", "start": 283.098, "end": 283.238}, {"text": " ", "start": 283.238, "end": 283.298}, {"text": "to,\"", "start": 283.298, "end": 284.138}, {"text": " ", "start": 284.138, "end": 284.259}, {"text": "<PERSON><PERSON><PERSON>", "start": 284.259, "end": 284.679}, {"text": " ", "start": 284.679, "end": 284.738}, {"text": "sa", "start": 284.738, "end": 284.838}, {"text": " ", "start": 284.838, "end": 284.899}, {"text": "mama.", "start": 284.899, "end": 285.259}, {"text": " ", "start": 285.259, "end": 285.959}, {"text": "„Tentoraz", "start": 285.959, "end": 286.478}, {"text": " ", "start": 286.478, "end": 286.518}, {"text": "potrebuje", "start": 286.518, "end": 286.958}, {"text": " ", "start": 286.958, "end": 287.058}, {"text": "š<PERSON><PERSON>", "start": 287.058, "end": 287.278}, {"text": " ", "start": 287.278, "end": 287.319}, {"text": "hodiny,", "start": 287.319, "end": 287.678}, {"text": " ", "start": 287.678, "end": 287.718}, {"text": "aby", "start": 287.718, "end": 287.858}, {"text": " ", "start": 287.858, "end": 287.918}, {"text": "sa", "start": 287.918, "end": 288.018}, {"text": " ", "start": 288.018, "end": 288.078}, {"text": "želé", "start": 288.078, "end": 288.298}, {"text": " ", "start": 288.298, "end": 288.338}, {"text": "dobre", "start": 288.338, "end": 288.598}, {"text": " ", "start": 288.598, "end": 288.658}, {"text": "stuhlo.\"", "start": 288.658, "end": 290.718}, {"text": "\n", "start": 290.718, "end": 290.778}, {"text": "\n", "start": 290.778, "end": 290.798}, {"text": "Keď", "start": 290.798, "end": 290.938}, {"text": " ", "start": 290.938, "end": 290.978}, {"text": "<PERSON><PERSON><PERSON>", "start": 290.978, "end": 291.278}, {"text": " ", "start": 291.278, "end": 291.319}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 291.319, "end": 291.658}, {"text": " ", "start": 291.658, "end": 291.738}, {"text": "otec", "start": 291.738, "end": 291.998}, {"text": " ", "start": 291.998, "end": 292.058}, {"text": "domov,", "start": 292.058, "end": 292.679}, {"text": " ", "start": 292.679, "end": 292.759}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 292.759, "end": 293.098}, {"text": " ", "start": 293.098, "end": 293.138}, {"text": "bol", "start": 293.138, "end": 293.278}, {"text": " ", "start": 293.278, "end": 293.319}, {"text": "prip<PERSON><PERSON>ý.", "start": 293.319, "end": 294.578}, {"text": " ", "start": 294.578, "end": 294.679}, {"text": "<PERSON>", "start": 294.679, "end": 294.899}, {"text": " ", "start": 294.899, "end": 294.959}, {"text": "ho", "start": 294.959, "end": 295.058}, {"text": " ", "start": 295.058, "end": 295.138}, {"text": "slávnostne", "start": 295.138, "end": 295.778}, {"text": " ", "start": 295.778, "end": 295.798}, {"text": "p<PERSON><PERSON><PERSON>", "start": 295.798, "end": 296.298}, {"text": " ", "start": 296.298, "end": 296.338}, {"text": "na", "start": 296.338, "end": 296.459}, {"text": " ", "start": 296.459, "end": 296.518}, {"text": "stôl", "start": 296.518, "end": 297.278}, {"text": " ", "start": 297.278, "end": 297.319}, {"text": "–", "start": 297.319, "end": 297.32}, {"text": " ", "start": 297.32, "end": 297.358}, {"text": "bol", "start": 297.358, "end": 297.539}, {"text": " ", "start": 297.539, "end": 297.598}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 297.598, "end": 298.259}, {"text": " ", "start": 298.259, "end": 298.358}, {"text": "s", "start": 298.358, "end": 298.378}, {"text": " ", "start": 298.378, "end": 298.459}, {"text": "tmavou", "start": 298.459, "end": 298.878}, {"text": " ", "start": 298.878, "end": 298.959}, {"text": "čokoládovou", "start": 298.959, "end": 299.598}, {"text": " ", "start": 299.598, "end": 299.638}, {"text": "vrstvou", "start": 299.638, "end": 300.078}, {"text": " ", "start": 300.078, "end": 300.118}, {"text": "na", "start": 300.118, "end": 300.218}, {"text": " ", "start": 300.218, "end": 300.278}, {"text": "spod<PERSON>,", "start": 300.278, "end": 301.138}, {"text": " ", "start": 301.138, "end": 301.238}, {"text": "bi<PERSON><PERSON>", "start": 301.238, "end": 301.698}, {"text": " ", "start": 301.698, "end": 301.759}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 301.759, "end": 302.319}, {"text": " ", "start": 302.319, "end": 302.378}, {"text": "vrstvou", "start": 302.378, "end": 302.798}, {"text": " ", "start": 302.798, "end": 302.858}, {"text": "v", "start": 302.858, "end": 302.878}, {"text": " ", "start": 302.878, "end": 302.959}, {"text": "strede", "start": 302.959, "end": 303.679}, {"text": " ", "start": 303.679, "end": 303.738}, {"text": "a", "start": 303.738, "end": 303.798}, {"text": " ", "start": 303.798, "end": 303.918}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 303.918, "end": 304.358}, {"text": " ", "start": 304.358, "end": 304.399}, {"text": "broskyňami", "start": 304.399, "end": 304.978}, {"text": " ", "start": 304.978, "end": 305.018}, {"text": "na", "start": 305.018, "end": 305.098}, {"text": " ", "start": 305.098, "end": 305.138}, {"text": "vrchu.", "start": 305.138, "end": 306.318}, {"text": "\n", "start": 306.318, "end": 306.358}, {"text": "\n", "start": 306.358, "end": 306.378}, {"text": "„Je", "start": 306.378, "end": 306.438}, {"text": " ", "start": 306.438, "end": 306.838}, {"text": "úžasný!\"", "start": 306.838, "end": 307.878}, {"text": " ", "start": 307.878, "end": 308.018}, {"text": "<PERSON><PERSON><PERSON>", "start": 308.018, "end": 308.399}, {"text": " ", "start": 308.399, "end": 308.478}, {"text": "otec", "start": 308.478, "end": 308.778}, {"text": " ", "start": 308.778, "end": 308.819}, {"text": "po", "start": 308.819, "end": 308.918}, {"text": " ", "start": 308.918, "end": 308.959}, {"text": "prvom", "start": 308.959, "end": 309.238}, {"text": " ", "start": 309.238, "end": 309.319}, {"text": "súste.", "start": 309.319, "end": 310.778}, {"text": " ", "start": 310.778, "end": 310.819}, {"text": "„Lenka,", "start": 310.819, "end": 311.959}, {"text": " ", "start": 311.959, "end": 312.058}, {"text": "ty", "start": 312.058, "end": 312.138}, {"text": " ", "start": 312.138, "end": 312.198}, {"text": "si", "start": 312.198, "end": 312.278}, {"text": " ", "start": 312.278, "end": 312.319}, {"text": "toto", "start": 312.319, "end": 312.538}, {"text": " ", "start": 312.538, "end": 312.578}, {"text": "pomáhala", "start": 312.578, "end": 313.098}, {"text": " ", "start": 313.098, "end": 313.158}, {"text": "robiť?\"", "start": 313.158, "end": 314.938}, {"text": "\n", "start": 314.938, "end": 314.998}, {"text": "\n", "start": 314.998, "end": 315.018}, {"text": "Len<PERSON>", "start": 315.018, "end": 315.138}, {"text": " ", "start": 315.138, "end": 315.179}, {"text": "hrdo", "start": 315.179, "end": 315.438}, {"text": " ", "start": 315.438, "end": 315.478}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>,", "start": 315.478, "end": 316.459}, {"text": " ", "start": 316.459, "end": 316.598}, {"text": "líca", "start": 316.598, "end": 316.959}, {"text": " ", "start": 316.959, "end": 317.018}, {"text": "mala", "start": 317.018, "end": 317.258}, {"text": " ", "start": 317.258, "end": 317.319}, {"text": "od", "start": 317.319, "end": 317.418}, {"text": " ", "start": 317.418, "end": 317.459}, {"text": "rado<PERSON>i", "start": 317.459, "end": 317.878}, {"text": " ", "start": 317.878, "end": 317.959}, {"text": "červené.", "start": 317.959, "end": 319.738}, {"text": " ", "start": 319.738, "end": 319.778}, {"text": "„<PERSON><PERSON>", "start": 319.778, "end": 320.018}, {"text": " ", "start": 320.018, "end": 320.078}, {"text": "ma", "start": 320.078, "end": 320.158}, {"text": " ", "start": 320.158, "end": 320.218}, {"text": "naučila,", "start": 320.218, "end": 320.738}, {"text": " ", "start": 320.738, "end": 320.819}, {"text": "ako", "start": 320.819, "end": 320.978}, {"text": " ", "start": 320.978, "end": 321.018}, {"text": "sa", "start": 321.018, "end": 321.138}, {"text": " ", "start": 321.138, "end": 321.179}, {"text": "p<PERSON>ž<PERSON>va", "start": 321.179, "end": 321.638}, {"text": " ", "start": 321.638, "end": 321.679}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 321.679, "end": 322.098}, {"text": " ", "start": 322.098, "end": 322.138}, {"text": "<PERSON><PERSON><PERSON>", "start": 322.138, "end": 322.578}, {"text": " ", "start": 322.578, "end": 322.638}, {"text": "odmerky!\"", "start": 322.638, "end": 324.618}, {"text": "\n", "start": 324.618, "end": 324.679}, {"text": "\n", "start": 324.679, "end": 324.698}, {"text": "„Moja", "start": 324.698, "end": 324.938}, {"text": " ", "start": 324.938, "end": 324.959}, {"text": "malá", "start": 324.959, "end": 325.258}, {"text": " ", "start": 325.258, "end": 325.438}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>,\"", "start": 325.438, "end": 326.838}, {"text": " ", "start": 326.838, "end": 326.959}, {"text": "zasmial", "start": 326.959, "end": 327.358}, {"text": " ", "start": 327.358, "end": 327.418}, {"text": "sa", "start": 327.418, "end": 327.518}, {"text": " ", "start": 327.518, "end": 327.658}, {"text": "otec.", "start": 327.658, "end": 328.878}, {"text": "\n", "start": 328.878, "end": 328.918}, {"text": "\n", "start": 328.918, "end": 328.938}, {"text": "„<PERSON><PERSON>,\"", "start": 328.938, "end": 329.998}, {"text": " ", "start": 329.998, "end": 330.098}, {"text": "povedala", "start": 330.098, "end": 330.518}, {"text": " ", "start": 330.518, "end": 330.658}, {"text": "Len<PERSON>", "start": 330.658, "end": 330.878}, {"text": " ", "start": 330.878, "end": 330.918}, {"text": "s", "start": 330.918, "end": 330.958}, {"text": " ", "start": 330.958, "end": 331.018}, {"text": "plnými", "start": 331.018, "end": 331.358}, {"text": " ", "start": 331.358, "end": 331.459}, {"text": "<PERSON><PERSON><PERSON>", "start": 331.459, "end": 331.798}, {"text": " ", "start": 331.798, "end": 331.858}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>,", "start": 331.858, "end": 333.018}, {"text": " ", "start": 333.018, "end": 333.118}, {"text": "„môžeme", "start": 333.118, "end": 333.498}, {"text": " ", "start": 333.498, "end": 333.539}, {"text": "zajtra", "start": 333.539, "end": 333.858}, {"text": " ", "start": 333.858, "end": 333.918}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 333.918, "end": 334.338}, {"text": " ", "start": 334.338, "end": 334.438}, {"text": "ďalší", "start": 334.438, "end": 334.718}, {"text": " ", "start": 334.718, "end": 335.018}, {"text": "hrnčekový", "start": 335.018, "end": 335.319}, {"text": " ", "start": 335.319, "end": 335.338}, {"text": "kol<PERSON><PERSON>?\"", "start": 335.338, "end": 337.319}, {"text": "\n", "start": 337.319, "end": 337.378}, {"text": "\n", "start": 337.378, "end": 337.399}, {"text": "<PERSON>", "start": 337.399, "end": 337.478}, {"text": " ", "start": 337.478, "end": 337.539}, {"text": "sa", "start": 337.539, "end": 337.638}, {"text": " ", "start": 337.638, "end": 337.679}, {"text": "p<PERSON><PERSON>a", "start": 337.679, "end": 338.058}, {"text": " ", "start": 338.058, "end": 338.118}, {"text": "na", "start": 338.118, "end": 338.198}, {"text": " ", "start": 338.198, "end": 338.278}, {"text": "otca", "start": 338.278, "end": 338.918}, {"text": " ", "start": 338.918, "end": 338.998}, {"text": "a", "start": 338.998, "end": 339.078}, {"text": " ", "start": 339.078, "end": 339.158}, {"text": "obaja", "start": 339.158, "end": 339.459}, {"text": " ", "start": 339.459, "end": 339.518}, {"text": "sa", "start": 339.518, "end": 339.618}, {"text": " ", "start": 339.618, "end": 339.698}, {"text": "usmiali.", "start": 339.698, "end": 341.098}, {"text": " ", "start": 341.098, "end": 341.138}, {"text": "„Samozrejme.", "start": 341.138, "end": 342.158}, {"text": " ", "start": 342.158, "end": 342.218}, {"text": "Poznám", "start": 342.218, "end": 342.558}, {"text": " ", "start": 342.558, "end": 342.618}, {"text": "<PERSON><PERSON><PERSON>", "start": 342.618, "end": 342.798}, {"text": " ", "start": 342.798, "end": 342.838}, {"text": "receptov", "start": 342.838, "end": 343.278}, {"text": " ", "start": 343.278, "end": 343.319}, {"text": "na", "start": 343.319, "end": 343.398}, {"text": " ", "start": 343.398, "end": 343.438}, {"text": "hrnčekové", "start": 343.438, "end": 343.978}, {"text": " ", "start": 343.978, "end": 343.998}, {"text": "koláče.", "start": 343.998, "end": 344.798}, {"text": " ", "start": 344.798, "end": 344.838}, {"text": "Možno", "start": 344.838, "end": 345.098}, {"text": " ", "start": 345.098, "end": 345.158}, {"text": "jahod<PERSON><PERSON>?\"", "start": 345.158, "end": 346.858}, {"text": "\n", "start": 346.858, "end": 346.918}, {"text": "\n", "start": 346.918, "end": 346.938}, {"text": "Lenkine", "start": 346.938, "end": 347.478}, {"text": " ", "start": 347.478, "end": 347.498}, {"text": "<PERSON><PERSON><PERSON>", "start": 347.498, "end": 347.698}, {"text": " ", "start": 347.698, "end": 347.758}, {"text": "sa", "start": 347.758, "end": 347.838}, {"text": " ", "start": 347.838, "end": 347.878}, {"text": "roz<PERSON><PERSON><PERSON>.", "start": 347.878, "end": 349.378}, {"text": " ", "start": 349.378, "end": 350.478}, {"text": "„Áno!\"", "start": 350.478, "end": 350.558}, {"text": " ", "start": 350.558, "end": 350.658}, {"text": "zakričala", "start": 350.658, "end": 366.679}, {"text": " ", "start": 366.679, "end": 366.698}, {"text": "nadšene.", "start": 366.698, "end": 366.838}, {"text": " ", "start": 366.838, "end": 366.878}, {"text": "„Jahodový", "start": 366.878, "end": 367.058}, {"text": " ", "start": 367.058, "end": 367.078}, {"text": "hrnčekový", "start": 367.078, "end": 367.258}, {"text": " ", "start": 367.258, "end": 367.278}, {"text": "kol<PERSON>č!\"", "start": 367.278, "end": 367.378}, {"text": "\n", "start": 367.378, "end": 367.438}, {"text": "\n", "start": 367.438, "end": 367.459}, {"text": "A", "start": 367.459, "end": 367.478}, {"text": " ", "start": 367.478, "end": 367.498}, {"text": "tak", "start": 367.498, "end": 367.558}, {"text": " ", "start": 367.558, "end": 367.578}, {"text": "sa", "start": 367.578, "end": 367.618}, {"text": " ", "start": 367.618, "end": 367.638}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 367.638, "end": 367.758}, {"text": " ", "start": 367.758, "end": 367.778}, {"text": "Lenkino", "start": 367.778, "end": 367.918}, {"text": " ", "start": 367.918, "end": 367.938}, {"text": "dobrodružstvo", "start": 367.938, "end": 368.198}, {"text": " ", "start": 368.198, "end": 368.218}, {"text": "so", "start": 368.218, "end": 368.258}, {"text": " ", "start": 368.258, "end": 368.278}, {"text": "slovenskými", "start": 368.278, "end": 368.498}, {"text": " ", "start": 368.498, "end": 368.518}, {"text": "hrnčekovými", "start": 368.518, "end": 368.738}, {"text": " ", "start": 368.738, "end": 368.758}, {"text": "koláčmi.", "start": 368.758, "end": 368.898}, {"text": " ", "start": 368.898, "end": 368.938}, {"text": "S", "start": 368.938, "end": 368.959}, {"text": " ", "start": 368.959, "end": 368.978}, {"text": "každ<PERSON><PERSON>", "start": 368.978, "end": 369.098}, {"text": " ", "start": 369.098, "end": 369.118}, {"text": "receptom", "start": 369.118, "end": 369.278}, {"text": " ", "start": 369.278, "end": 369.298}, {"text": "sa", "start": 369.298, "end": 369.338}, {"text": " ", "start": 369.338, "end": 369.358}, {"text": "učila", "start": 369.358, "end": 369.459}, {"text": " ", "start": 369.459, "end": 369.478}, {"text": "nové", "start": 369.478, "end": 369.558}, {"text": " ", "start": 369.558, "end": 369.578}, {"text": "slovenské", "start": 369.578, "end": 369.758}, {"text": " ", "start": 369.758, "end": 369.778}, {"text": "slová", "start": 369.778, "end": 369.878}, {"text": " ", "start": 369.878, "end": 369.898}, {"text": "a", "start": 369.898, "end": 369.918}, {"text": " ", "start": 369.918, "end": 369.938}, {"text": "nové", "start": 369.938, "end": 370.018}, {"text": " ", "start": 370.018, "end": 370.039}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 370.039, "end": 370.218}, {"text": " ", "start": 370.218, "end": 370.238}, {"text": "triky.", "start": 370.238, "end": 370.338}, {"text": " ", "start": 370.338, "end": 370.378}, {"text": "A", "start": 370.378, "end": 370.398}, {"text": " ", "start": 370.398, "end": 370.418}, {"text": "najlepšie", "start": 370.418, "end": 370.598}, {"text": " ", "start": 370.598, "end": 370.618}, {"text": "na", "start": 370.618, "end": 370.658}, {"text": " ", "start": 370.658, "end": 370.679}, {"text": "tom", "start": 370.679, "end": 370.738}, {"text": " ", "start": 370.738, "end": 370.758}, {"text": "bolo,", "start": 370.758, "end": 370.838}, {"text": " ", "start": 370.838, "end": 370.878}, {"text": "že", "start": 370.878, "end": 370.918}, {"text": " ", "start": 370.918, "end": 370.938}, {"text": "<PERSON><PERSON><PERSON><PERSON>,", "start": 370.938, "end": 371.058}, {"text": " ", "start": 371.058, "end": 371.098}, {"text": "čo", "start": 371.098, "end": 371.138}, {"text": " ", "start": 371.138, "end": 371.158}, {"text": "potrebovala,", "start": 371.158, "end": 371.378}, {"text": " ", "start": 371.378, "end": 371.418}, {"text": "bol", "start": 371.418, "end": 371.478}, {"text": " ", "start": 371.478, "end": 371.498}, {"text": "jeden", "start": 371.498, "end": 371.598}, {"text": " ", "start": 371.598, "end": 371.618}, {"text": "obyčajný", "start": 371.618, "end": 371.778}, {"text": " ", "start": 371.778, "end": 371.798}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 371.798, "end": 371.918}, {"text": " ", "start": 371.918, "end": 371.938}, {"text": "a", "start": 371.938, "end": 371.959}, {"text": " ", "start": 371.959, "end": 371.978}, {"text": "mamina", "start": 371.978, "end": 372.098}, {"text": " ", "start": 372.098, "end": 372.118}, {"text": "pomoc.", "start": 372.118, "end": 372.218}]}