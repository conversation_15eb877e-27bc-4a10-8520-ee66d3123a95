'use server';

import { headers } from 'next/headers';
import { DeviceState } from '@/app/providers/DeviceProvider';

export async function getServerDeviceInfo(): Promise<DeviceState> {
  const headersList = await headers();
  const userAgent = headersList.get('user-agent') || '';

  const isMobileDevice =
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);

  return {
    isMobile: isMobileDevice,
    isDesktop: !isMobileDevice,
  };
}
