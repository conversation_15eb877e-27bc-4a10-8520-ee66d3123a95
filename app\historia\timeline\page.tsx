'use client';

import { useState } from 'react';
import { LayoutWrapper } from '@/components/LayoutWrapper';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  CalendarIcon, 
  MapIcon, 
  UsersIcon,
  ScrollIcon,
  ChevronRightIcon,
  ChevronLeftIcon,
  InfoIcon
} from 'lucide-react';

interface TimelineEvent {
  id: string;
  year: string;
  title: string;
  englishTitle: string;
  description: string;
  englishDescription: string;
  category: 'political' | 'cultural' | 'religious' | 'military';
  importance: 'low' | 'medium' | 'high';
  details: string[];
  relatedFigures: string[];
  location: string;
}

const timelineEvents: TimelineEvent[] = [
  {
    id: 'slavs-arrival',
    year: '5.-6. storočie',
    title: '<PERSON>r<PERSON><PERSON><PERSON>',
    englishTitle: 'Arrival of Slavs',
    description: 'Slovanské kmene sa usídľujú na území dnešného Slovenska',
    englishDescription: 'Slavic tribes settle in the territory of present-day Slovakia',
    category: 'political',
    importance: 'high',
    details: [
      'Migrácia z východoeurópskych stepí',
      'Osídlenie úrodných oblastí okolo riek',
      'Zmiešanie s pôvodným obyvateľstvom',
      'Vznik prvých slovanských osád'
    ],
    relatedFigures: ['Slovanské kmene'],
    location: 'Podunajská nížina'
  },
  {
    id: 'samo-empire',
    year: '623-658',
    title: 'Samova ríša',
    englishTitle: 'Samo\'s Empire',
    description: 'Vznik prvého slovanského štátneho útvaru',
    englishDescription: 'Formation of the first Slavic state entity',
    category: 'political',
    importance: 'high',
    details: [
      'Samo sa stáva kráľom Slovanov',
      'Víťazstvo nad Avarmi',
      'Rozšírenie ríše na veľké územie',
      'Rozvoj obchodu a remesiel'
    ],
    relatedFigures: ['Samo', 'Avari'],
    location: 'Stredná Európa'
  },
  {
    id: 'great-moravia-foundation',
    year: '833',
    title: 'Vznik Veľkej Moravy',
    englishTitle: 'Foundation of Great Moravia',
    description: 'Mojmír I. zakladá Veľkomoravskú ríšu',
    englishDescription: 'Mojmír I establishes the Great Moravian Empire',
    category: 'political',
    importance: 'high',
    details: [
      'Zjednotenie moravských kniežat',
      'Rozšírenie na slovenské územie',
      'Vytvorenie silnej centrálnej moci',
      'Začiatok zlatého veku'
    ],
    relatedFigures: ['Mojmír I.'],
    location: 'Morava, Slovensko'
  },
  {
    id: 'cyril-methodius',
    year: '863',
    title: 'Príchod Cyrila a Metoda',
    englishTitle: 'Arrival of Cyril and Methodius',
    description: 'Byzantskí misionári prinášajú písomnosť a kresťanstvo',
    englishDescription: 'Byzantine missionaries bring literacy and Christianity',
    category: 'cultural',
    importance: 'high',
    details: [
      'Vytvorenie glagolského písma',
      'Preklad Biblie do staroslovienčiny',
      'Založenie prvých škôl',
      'Rozvoj slovanskej kultúry'
    ],
    relatedFigures: ['Cyril', 'Metod', 'Rastislav'],
    location: 'Veľká Morava'
  },
  {
    id: 'svatopluk-reign',
    year: '870-894',
    title: 'Vláda Svätopluka I.',
    englishTitle: 'Reign of Svatopluk I',
    description: 'Veľká Morava dosahuje najväčší územný rozsah',
    englishDescription: 'Great Moravia reaches its greatest territorial extent',
    category: 'political',
    importance: 'high',
    details: [
      'Rozšírenie ríše na najväčšie územie',
      'Diplomatické kontakty s Byzanciou',
      'Posilnenie kresťanstva',
      'Rozkvět kultúry a vzdelanosti'
    ],
    relatedFigures: ['Svätopluk I.'],
    location: 'Veľká Morava'
  },
  {
    id: 'great-moravia-fall',
    year: '907',
    title: 'Zánik Veľkej Moravy',
    englishTitle: 'Fall of Great Moravia',
    description: 'Maďarské kmene ničia Veľkomoravskú ríšu',
    englishDescription: 'Hungarian tribes destroy the Great Moravian Empire',
    category: 'military',
    importance: 'high',
    details: [
      'Nájazdy maďarských kmeňov',
      'Rozpad centrálnej moci',
      'Koniec slovanskej štátnosti',
      'Začiatok nového obdobia'
    ],
    relatedFigures: ['Maďarské kmene'],
    location: 'Veľká Morava'
  }
];

const categoryColors = {
  political: 'bg-blue-500',
  cultural: 'bg-purple-500',
  religious: 'bg-yellow-500',
  military: 'bg-red-500'
};

const categoryLabels = {
  political: 'Politické',
  cultural: 'Kultúrne',
  religious: 'Náboženské',
  military: 'Vojenské'
};

const importanceColors = {
  low: 'border-gray-300',
  medium: 'border-yellow-400',
  high: 'border-red-500'
};

export default function TimelinePage() {
  const [selectedEvent, setSelectedEvent] = useState<TimelineEvent | null>(null);
  const [currentIndex, setCurrentIndex] = useState(0);

  const nextEvent = () => {
    if (currentIndex < timelineEvents.length - 1) {
      setCurrentIndex(currentIndex + 1);
      setSelectedEvent(timelineEvents[currentIndex + 1]);
    }
  };

  const prevEvent = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
      setSelectedEvent(timelineEvents[currentIndex - 1]);
    }
  };

  return (
    <LayoutWrapper>
      <div className="max-w-7xl mx-auto pb-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Interaktívna časová os</h1>
          <p className="text-muted-foreground">
            Preskúmajte kľúčové udalosti slovenskej histórie v chronologickom poradí
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Timeline */}
          <div className="lg:col-span-2">
            <div className="relative">
              {/* Timeline line */}
              <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-border"></div>
              
              <div className="space-y-8">
                {timelineEvents.map((event, index) => (
                  <div 
                    key={event.id} 
                    className={`relative cursor-pointer transition-all duration-200 ${
                      selectedEvent?.id === event.id ? 'scale-105' : 'hover:scale-102'
                    }`}
                    onClick={() => {
                      setSelectedEvent(event);
                      setCurrentIndex(index);
                    }}
                  >
                    {/* Timeline dot */}
                    <div className={`absolute left-6 w-4 h-4 rounded-full border-4 ${
                      selectedEvent?.id === event.id 
                        ? 'bg-primary border-primary' 
                        : `bg-background ${importanceColors[event.importance]}`
                    }`}></div>
                    
                    {/* Event card */}
                    <div className="ml-16">
                      <Card className={`${
                        selectedEvent?.id === event.id ? 'ring-2 ring-primary shadow-lg' : ''
                      }`}>
                        <CardHeader className="pb-3">
                          <div className="flex items-center gap-2 mb-2">
                            <Badge variant="outline">{event.year}</Badge>
                            <Badge className={`${categoryColors[event.category]} text-white`}>
                              {categoryLabels[event.category]}
                            </Badge>
                          </div>
                          <CardTitle className="text-lg">{event.title}</CardTitle>
                          <p className="text-sm text-muted-foreground italic">
                            {event.englishTitle}
                          </p>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm">{event.description}</p>
                          <p className="text-xs text-muted-foreground italic mt-1">
                            {event.englishDescription}
                          </p>
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Event Details */}
          <div className="lg:col-span-1">
            {selectedEvent ? (
              <div className="sticky top-24">
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between mb-2">
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={prevEvent}
                        disabled={currentIndex === 0}
                      >
                        <ChevronLeftIcon className="h-4 w-4" />
                      </Button>
                      <Badge variant="outline">
                        {currentIndex + 1} / {timelineEvents.length}
                      </Badge>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={nextEvent}
                        disabled={currentIndex === timelineEvents.length - 1}
                      >
                        <ChevronRightIcon className="h-4 w-4" />
                      </Button>
                    </div>
                    <CardTitle className="flex items-center gap-2">
                      <InfoIcon className="h-5 w-5" />
                      Detaily udalosti
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2 flex items-center gap-2">
                        <ScrollIcon className="h-4 w-4" />
                        Kľúčové body
                      </h4>
                      <ul className="space-y-1">
                        {selectedEvent.details.map((detail, index) => (
                          <li key={index} className="flex items-start gap-2 text-sm">
                            <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0" />
                            <span>{detail}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2 flex items-center gap-2">
                        <UsersIcon className="h-4 w-4" />
                        Kľúčové osobnosti
                      </h4>
                      <div className="flex flex-wrap gap-1">
                        {selectedEvent.relatedFigures.map((figure, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {figure}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2 flex items-center gap-2">
                        <MapIcon className="h-4 w-4" />
                        Miesto
                      </h4>
                      <p className="text-sm text-muted-foreground">{selectedEvent.location}</p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : (
              <Card>
                <CardContent className="text-center py-8">
                  <CalendarIcon className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-muted-foreground">
                    Kliknite na udalosť v časovej osi pre zobrazenie detailov
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </LayoutWrapper>
  );
}
